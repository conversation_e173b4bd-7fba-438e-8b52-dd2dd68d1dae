import { useState, useEffect } from "react";
import { SEOClusterTool } from "@/components/SEOClusterTool";
import { AuthorBioBuilder } from "@/components/AuthorBioBuilder";
import { ContentBriefGenerator } from "@/components/ContentBriefGenerator";
import { SEOContentGenerator } from "@/components/SEOContentGenerator";
import { UnifiedFooter } from "@/components/UnifiedFooter";
import { ApiConfigDialog, ApiConfig } from "@/components/ApiConfigDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Search, Sparkles, FileText, Settings, Brain, PenTool } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Index = () => {
  const [activeTab, setActiveTab] = useState<'seo' | 'bio' | 'brief' | 'content'>('seo');
  const [apiConfig, setApiConfig] = useState<ApiConfig | null>(null);
  const [showApiDialog, setShowApiDialog] = useState(false);
  const { toast } = useToast();

  // Load saved API configuration on component mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('ai_api_config');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setApiConfig(config);
      } catch (error) {
        console.error('Failed to parse saved API config:', error);
        // Fallback to old Gemini key if exists
        const oldApiKey = localStorage.getItem('gemini_api_key');
        if (oldApiKey) {
          setApiConfig({
            provider: 'google',
            model: 'gemini-1.5-flash-latest',
            apiKey: oldApiKey
          });
        }
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
      {/* Navigation Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-xl sticky top-0 z-50 shadow-soft">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="relative p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
                  <Sparkles className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                    SEO Content Suite
                  </h1>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-muted-foreground">AI-Powered Content & SEO Tools</p>
                    {apiConfig && (
                      <div className="flex items-center space-x-1">
                        <Badge variant="secondary" className="text-xs">
                          {apiConfig.provider === 'google' ? 'Gemini' :
                           apiConfig.provider === 'openai' ? 'OpenAI' :
                           apiConfig.provider === 'anthropic' ? 'Claude' :
                           apiConfig.provider === 'xai' ? 'Grok' :
                           apiConfig.provider === 'deepseek' ? 'DeepSeek' :
                           apiConfig.provider === 'openrouter' ? 'OpenRouter' : apiConfig.provider}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {apiConfig.model.includes('gpt-4o') ? 'GPT-4o' :
                           apiConfig.model.includes('gpt-4') ? 'GPT-4' :
                           apiConfig.model.includes('gpt-3.5') ? 'GPT-3.5' :
                           apiConfig.model.includes('claude-3-5-sonnet') ? 'Claude 3.5 Sonnet' :
                           apiConfig.model.includes('claude-3-5-haiku') ? 'Claude 3.5 Haiku' :
                           apiConfig.model.includes('claude-3-opus') ? 'Claude 3 Opus' :
                           apiConfig.model.includes('claude-3-sonnet') ? 'Claude 3 Sonnet' :
                           apiConfig.model.includes('claude-3-haiku') ? 'Claude 3 Haiku' :
                           apiConfig.model.includes('gemini-1.5-pro') ? 'Gemini 1.5 Pro' :
                           apiConfig.model.includes('gemini-1.5-flash') ? 'Gemini 1.5 Flash' :
                           apiConfig.model.includes('gemini-pro') ? 'Gemini Pro' :
                           apiConfig.model.includes('grok-2') ? 'Grok-2' :
                           apiConfig.model.includes('grok-beta') ? 'Grok Beta' :
                           apiConfig.model.includes('deepseek-v2') ? 'DeepSeek V2' :
                           apiConfig.model.includes('deepseek-coder') ? 'DeepSeek Coder' :
                           apiConfig.model.includes('deepseek-chat') ? 'DeepSeek Chat' :
                           apiConfig.model.includes('llama-3.1-405b') ? 'Llama 3.1 405B' :
                           apiConfig.model.includes('llama-3.1-70b') ? 'Llama 3.1 70B' :
                           apiConfig.model.includes('llama-3.1-8b') ? 'Llama 3.1 8B' :
                           apiConfig.model.includes('mistral-large') ? 'Mistral Large' :
                           apiConfig.model.includes('mistral-medium') ? 'Mistral Medium' :
                           apiConfig.model.includes('command-r-plus') ? 'Command R+' :
                           apiConfig.model.includes('openai/') ? 'OpenAI (OR)' :
                           apiConfig.model.includes('anthropic/') ? 'Claude (OR)' :
                           apiConfig.model.includes('google/') ? 'Gemini (OR)' :
                           apiConfig.model.includes('meta-llama/') ? 'Llama (OR)' :
                           apiConfig.model.includes('mistralai/') ? 'Mistral (OR)' :
                           apiConfig.model}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* API Configuration Button */}
            <div className="flex items-center space-x-3">
              {apiConfig && (
                <div className="hidden md:flex items-center space-x-2 text-xs text-muted-foreground">
                  <span>Using:</span>
                  <Badge variant="secondary" className="text-xs">
                    {apiConfig.provider === 'google' ? 'Gemini' :
                     apiConfig.provider === 'openai' ? 'OpenAI' :
                     apiConfig.provider === 'anthropic' ? 'Claude' :
                     apiConfig.provider === 'xai' ? 'Grok' :
                     apiConfig.provider === 'deepseek' ? 'DeepSeek' :
                     apiConfig.provider === 'openrouter' ? 'OpenRouter' : apiConfig.provider}
                  </Badge>
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApiDialog(true)}
                className="flex items-center space-x-2"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">{apiConfig ? 'Change AI' : 'Setup AI'}</span>
                {apiConfig ? (
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                ) : (
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Tool Selection */}
      <div className="container mx-auto px-4 py-6">
        {/* Modern Tool Selection Cards */}
        <div className="mb-8">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
              Choose Your Professional Tool
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Select from our suite of professional SEO and content creation tools, powered by 75+ AI models.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {/* SEO Cluster Tool */}
            <Card
              className={`group cursor-pointer transition-all duration-300 hover-lift ${
                activeTab === 'seo'
                  ? 'ring-2 ring-primary shadow-glow bg-gradient-card'
                  : 'hover:shadow-elegant'
              }`}
              onClick={() => setActiveTab('seo')}
            >
              <CardContent className="p-6 text-center">
                <div className={`mx-auto w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 ${
                  activeTab === 'seo'
                    ? 'bg-gradient-primary shadow-glow'
                    : 'bg-muted group-hover:bg-gradient-primary group-hover:shadow-glow'
                }`}>
                  <Search className={`h-8 w-8 transition-colors duration-300 ${
                    activeTab === 'seo' ? 'text-white' : 'text-muted-foreground group-hover:text-white'
                  }`} />
                </div>
                <h3 className="text-lg font-semibold mb-2">SEO Cluster Tool</h3>
                <p className="text-sm text-muted-foreground">
                  Generate comprehensive keyword clusters and content strategies
                </p>
              </CardContent>
            </Card>

            {/* SEO Content Generator */}
            <Card
              className={`group cursor-pointer transition-all duration-300 hover-lift ${
                activeTab === 'content'
                  ? 'ring-2 ring-primary shadow-glow bg-gradient-card'
                  : 'hover:shadow-elegant'
              }`}
              onClick={() => setActiveTab('content')}
            >
              <CardContent className="p-6 text-center">
                <div className={`mx-auto w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 ${
                  activeTab === 'content'
                    ? 'bg-gradient-primary shadow-glow'
                    : 'bg-muted group-hover:bg-gradient-primary group-hover:shadow-glow'
                }`}>
                  <PenTool className={`h-8 w-8 transition-colors duration-300 ${
                    activeTab === 'content' ? 'text-white' : 'text-muted-foreground group-hover:text-white'
                  }`} />
                </div>
                <h3 className="text-lg font-semibold mb-2">SEO Content Generator</h3>
                <p className="text-sm text-muted-foreground">
                  Create E-E-A-T compliant, SEO-optimized long-form articles
                </p>
              </CardContent>
            </Card>

            {/* Content Brief Generator */}
            <Card
              className={`group cursor-pointer transition-all duration-300 hover-lift ${
                activeTab === 'brief'
                  ? 'ring-2 ring-primary shadow-glow bg-gradient-card'
                  : 'hover:shadow-elegant'
              }`}
              onClick={() => setActiveTab('brief')}
            >
              <CardContent className="p-6 text-center">
                <div className={`mx-auto w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 ${
                  activeTab === 'brief'
                    ? 'bg-gradient-primary shadow-glow'
                    : 'bg-muted group-hover:bg-gradient-primary group-hover:shadow-glow'
                }`}>
                  <FileText className={`h-8 w-8 transition-colors duration-300 ${
                    activeTab === 'brief' ? 'text-white' : 'text-muted-foreground group-hover:text-white'
                  }`} />
                </div>
                <h3 className="text-lg font-semibold mb-2">Content Brief Generator</h3>
                <p className="text-sm text-muted-foreground">
                  Generate detailed content briefs and outlines for writers
                </p>
              </CardContent>
            </Card>

            {/* Author Bio Builder */}
            <Card
              className={`group cursor-pointer transition-all duration-300 hover-lift ${
                activeTab === 'bio'
                  ? 'ring-2 ring-primary shadow-glow bg-gradient-card'
                  : 'hover:shadow-elegant'
              }`}
              onClick={() => setActiveTab('bio')}
            >
              <CardContent className="p-6 text-center">
                <div className={`mx-auto w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 ${
                  activeTab === 'bio'
                    ? 'bg-gradient-primary shadow-glow'
                    : 'bg-muted group-hover:bg-gradient-primary group-hover:shadow-glow'
                }`}>
                  <User className={`h-8 w-8 transition-colors duration-300 ${
                    activeTab === 'bio' ? 'text-white' : 'text-muted-foreground group-hover:text-white'
                  }`} />
                </div>
                <h3 className="text-lg font-semibold mb-2">Author Bio Builder</h3>
                <p className="text-sm text-muted-foreground">
                  Create professional author bios and expert personas
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enhanced API Configuration Notice */}
        {!apiConfig && (
          <Card className="mb-8 border-2 border-dashed border-primary/30 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 animate-fade-in">
            <CardContent className="p-8">
              <div className="text-center space-y-6">
                <div className="mx-auto w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center shadow-glow">
                  <Brain className="h-10 w-10 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gradient-primary mb-2">AI Configuration Required</h3>
                  <p className="text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                    Choose your preferred AI provider from our extensive selection to unlock the full potential of our SEO content suite.
                  </p>
                </div>
                <div className="flex flex-wrap justify-center gap-3 mb-6">
                  <Badge variant="outline" className="px-3 py-1">OpenAI GPT-4o</Badge>
                  <Badge variant="outline" className="px-3 py-1">Claude 3.5 Sonnet</Badge>
                  <Badge variant="outline" className="px-3 py-1">Gemini 2.0 Flash</Badge>
                  <Badge variant="outline" className="px-3 py-1">Grok-3 Beta</Badge>
                  <Badge variant="outline" className="px-3 py-1">DeepSeek V3</Badge>
                  <Badge variant="outline" className="px-3 py-1">OpenRouter</Badge>
                </div>
                <Button
                  onClick={() => setShowApiDialog(true)}
                  size="lg"
                  className="bg-gradient-primary hover:shadow-glow transition-all duration-300 hover-lift"
                >
                  <Settings className="h-5 w-5 mr-2" />
                  Configure AI Provider
                  <Sparkles className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tool Content */}
        {activeTab === 'seo' && (
          <SEOClusterTool
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
        {activeTab === 'content' && (
          <SEOContentGenerator
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
        {activeTab === 'brief' && (
          <ContentBriefGenerator
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
        {activeTab === 'bio' && (
          <AuthorBioBuilder
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
      </div>

      <UnifiedFooter />

      <ApiConfigDialog
        open={showApiDialog}
        onOpenChange={setShowApiDialog}
        onConfigSaved={(config) => {
          setApiConfig(config);
          localStorage.setItem('ai_api_config', JSON.stringify(config));
          toast({
            title: "API Configuration Saved",
            description: `Now using ${config.provider === 'google' ? 'Gemini' :
                         config.provider === 'openai' ? 'OpenAI' :
                         config.provider === 'anthropic' ? 'Claude' :
                         config.provider === 'xai' ? 'Grok' :
                         config.provider === 'deepseek' ? 'DeepSeek' :
                         config.provider === 'openrouter' ? 'OpenRouter' : config.provider} for all tools.`,
          });
        }}
        currentConfig={apiConfig || undefined}
      />
    </div>
  );
};

export default Index;
