import { useState, useEffect } from "react";
import { SEOClusterTool } from "@/components/SEOClusterTool";
import { AuthorBioBuilder } from "@/components/AuthorBioBuilder";
import { ContentBriefGenerator } from "@/components/ContentBriefGenerator";
import { UnifiedFooter } from "@/components/UnifiedFooter";
import { ApiConfigDialog, ApiConfig } from "@/components/ApiConfigDialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Search, Sparkles, FileText, Settings, Brain } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Index = () => {
  const [activeTab, setActiveTab] = useState<'seo' | 'bio' | 'brief'>('seo');
  const [apiConfig, setApiConfig] = useState<ApiConfig | null>(null);
  const [showApiDialog, setShowApiDialog] = useState(false);
  const { toast } = useToast();

  // Load saved API configuration on component mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('ai_api_config');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        setApiConfig(config);
      } catch (error) {
        console.error('Failed to parse saved API config:', error);
        // Fallback to old Gemini key if exists
        const oldApiKey = localStorage.getItem('gemini_api_key');
        if (oldApiKey) {
          setApiConfig({
            provider: 'google',
            model: 'gemini-1.5-flash-latest',
            apiKey: oldApiKey
          });
        }
      }
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
      {/* Navigation Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-xl sticky top-0 z-50 shadow-soft">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="relative p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
                  <Sparkles className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                    SEO Content Suite
                  </h1>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-muted-foreground">AI-Powered Content & SEO Tools</p>
                    {apiConfig && (
                      <div className="flex items-center space-x-1">
                        <Badge variant="secondary" className="text-xs">
                          {apiConfig.provider === 'google' ? 'Gemini' :
                           apiConfig.provider === 'openai' ? 'OpenAI' :
                           apiConfig.provider === 'anthropic' ? 'Claude' :
                           apiConfig.provider === 'xai' ? 'Grok' :
                           apiConfig.provider === 'deepseek' ? 'DeepSeek' :
                           apiConfig.provider === 'openrouter' ? 'OpenRouter' : apiConfig.provider}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {apiConfig.model.includes('gpt-4o') ? 'GPT-4o' :
                           apiConfig.model.includes('gpt-4') ? 'GPT-4' :
                           apiConfig.model.includes('gpt-3.5') ? 'GPT-3.5' :
                           apiConfig.model.includes('claude-3-5-sonnet') ? 'Claude 3.5 Sonnet' :
                           apiConfig.model.includes('claude-3-5-haiku') ? 'Claude 3.5 Haiku' :
                           apiConfig.model.includes('claude-3-opus') ? 'Claude 3 Opus' :
                           apiConfig.model.includes('claude-3-sonnet') ? 'Claude 3 Sonnet' :
                           apiConfig.model.includes('claude-3-haiku') ? 'Claude 3 Haiku' :
                           apiConfig.model.includes('gemini-1.5-pro') ? 'Gemini 1.5 Pro' :
                           apiConfig.model.includes('gemini-1.5-flash') ? 'Gemini 1.5 Flash' :
                           apiConfig.model.includes('gemini-pro') ? 'Gemini Pro' :
                           apiConfig.model.includes('grok-2') ? 'Grok-2' :
                           apiConfig.model.includes('grok-beta') ? 'Grok Beta' :
                           apiConfig.model.includes('deepseek-v2') ? 'DeepSeek V2' :
                           apiConfig.model.includes('deepseek-coder') ? 'DeepSeek Coder' :
                           apiConfig.model.includes('deepseek-chat') ? 'DeepSeek Chat' :
                           apiConfig.model.includes('llama-3.1-405b') ? 'Llama 3.1 405B' :
                           apiConfig.model.includes('llama-3.1-70b') ? 'Llama 3.1 70B' :
                           apiConfig.model.includes('llama-3.1-8b') ? 'Llama 3.1 8B' :
                           apiConfig.model.includes('mistral-large') ? 'Mistral Large' :
                           apiConfig.model.includes('mistral-medium') ? 'Mistral Medium' :
                           apiConfig.model.includes('command-r-plus') ? 'Command R+' :
                           apiConfig.model.includes('openai/') ? 'OpenAI (OR)' :
                           apiConfig.model.includes('anthropic/') ? 'Claude (OR)' :
                           apiConfig.model.includes('google/') ? 'Gemini (OR)' :
                           apiConfig.model.includes('meta-llama/') ? 'Llama (OR)' :
                           apiConfig.model.includes('mistralai/') ? 'Mistral (OR)' :
                           apiConfig.model}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* API Configuration Button */}
            <div className="flex items-center space-x-3">
              {apiConfig && (
                <div className="hidden md:flex items-center space-x-2 text-xs text-muted-foreground">
                  <span>Using:</span>
                  <Badge variant="secondary" className="text-xs">
                    {apiConfig.provider === 'google' ? 'Gemini' :
                     apiConfig.provider === 'openai' ? 'OpenAI' :
                     apiConfig.provider === 'anthropic' ? 'Claude' :
                     apiConfig.provider === 'xai' ? 'Grok' :
                     apiConfig.provider === 'deepseek' ? 'DeepSeek' :
                     apiConfig.provider === 'openrouter' ? 'OpenRouter' : apiConfig.provider}
                  </Badge>
                </div>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApiDialog(true)}
                className="flex items-center space-x-2"
              >
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">{apiConfig ? 'Change AI' : 'Setup AI'}</span>
                {apiConfig ? (
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                ) : (
                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Tool Selection */}
      <div className="container mx-auto px-4 py-6">
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant={activeTab === 'seo' ? 'default' : 'outline'}
                size="lg"
                onClick={() => setActiveTab('seo')}
                className="flex items-center space-x-2 min-w-[200px]"
              >
                <Search className="h-5 w-5" />
                <span>SEO Cluster Tool</span>
              </Button>
              <Button
                variant={activeTab === 'brief' ? 'default' : 'outline'}
                size="lg"
                onClick={() => setActiveTab('brief')}
                className="flex items-center space-x-2 min-w-[200px]"
              >
                <FileText className="h-5 w-5" />
                <span>Content Brief Generator</span>
              </Button>
              <Button
                variant={activeTab === 'bio' ? 'default' : 'outline'}
                size="lg"
                onClick={() => setActiveTab('bio')}
                className="flex items-center space-x-2 min-w-[200px]"
              >
                <User className="h-5 w-5" />
                <span>Author Bio Builder</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* API Configuration Notice */}
        {!apiConfig && (
          <Card className="mb-6 border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20">
            <CardContent className="p-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-orange-100 dark:bg-orange-800 rounded-lg">
                  <Settings className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-orange-800 dark:text-orange-200">AI Configuration Required</h3>
                  <p className="text-sm text-orange-600 dark:text-orange-400 mt-1">
                    Choose your preferred AI provider (OpenAI, Claude, Gemini, Grok, DeepSeek, or OpenRouter) to start generating content.
                  </p>
                </div>
                <Button
                  onClick={() => setShowApiDialog(true)}
                  className="bg-orange-600 hover:bg-orange-700 text-white"
                >
                  <Brain className="h-4 w-4 mr-2" />
                  Setup AI Provider
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tool Content */}
        {activeTab === 'seo' && (
          <SEOClusterTool
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
        {activeTab === 'brief' && (
          <ContentBriefGenerator
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
        {activeTab === 'bio' && (
          <AuthorBioBuilder
            apiConfig={apiConfig}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
      </div>

      <UnifiedFooter />

      <ApiConfigDialog
        open={showApiDialog}
        onOpenChange={setShowApiDialog}
        onConfigSaved={(config) => {
          setApiConfig(config);
          localStorage.setItem('ai_api_config', JSON.stringify(config));
          toast({
            title: "API Configuration Saved",
            description: `Now using ${config.provider === 'google' ? 'Gemini' :
                         config.provider === 'openai' ? 'OpenAI' :
                         config.provider === 'anthropic' ? 'Claude' :
                         config.provider === 'xai' ? 'Grok' :
                         config.provider === 'deepseek' ? 'DeepSeek' :
                         config.provider === 'openrouter' ? 'OpenRouter' : config.provider} for all tools.`,
          });
        }}
        currentConfig={apiConfig || undefined}
      />
    </div>
  );
};

export default Index;
