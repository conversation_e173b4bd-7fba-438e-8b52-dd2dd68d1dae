import { useState, useEffect } from "react";
import { SEOClusterTool } from "@/components/SEOClusterTool";
import { AuthorBioBuilder } from "@/components/AuthorBioBuilder";
import { ApiKeyDialog } from "@/components/ApiKeyDialog";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { User, Search, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Index = () => {
  const [activeTab, setActiveTab] = useState<'seo' | 'bio'>('seo');
  const [apiKey, setApiKey] = useState("");
  const [showApiDialog, setShowApiDialog] = useState(false);
  const { toast } = useToast();

  // Load saved API key on component mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('gemini_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-blue-900 dark:to-indigo-900">
      {/* Navigation Header */}
      <header className="border-b border-border bg-card/50 backdrop-blur-xl sticky top-0 z-50 shadow-soft">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="relative p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
                  <Sparkles className="h-7 w-7 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                    SEO Content Suite
                  </h1>
                  <p className="text-sm text-muted-foreground">AI-Powered Content & SEO Tools</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Tool Selection */}
      <div className="container mx-auto px-4 py-6">
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant={activeTab === 'seo' ? 'default' : 'outline'}
                size="lg"
                onClick={() => setActiveTab('seo')}
                className="flex items-center space-x-2 min-w-[200px]"
              >
                <Search className="h-5 w-5" />
                <span>SEO Cluster Tool</span>
              </Button>
              <Button
                variant={activeTab === 'bio' ? 'default' : 'outline'}
                size="lg"
                onClick={() => setActiveTab('bio')}
                className="flex items-center space-x-2 min-w-[200px]"
              >
                <User className="h-5 w-5" />
                <span>Author Bio Builder</span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tool Content */}
        {activeTab === 'seo' && (
          <SEOClusterTool
            apiKey={apiKey}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
        {activeTab === 'bio' && (
          <AuthorBioBuilder
            apiKey={apiKey}
            onApiKeyRequest={() => setShowApiDialog(true)}
          />
        )}
      </div>

      <ApiKeyDialog
        open={showApiDialog}
        onOpenChange={setShowApiDialog}
        onApiKeySaved={(key) => {
          setApiKey(key);
          if (key) {
            localStorage.setItem('gemini_api_key', key);
            toast({
              title: "API Key Saved",
              description: "You can now use both SEO and Bio tools.",
            });
          } else {
            localStorage.removeItem('gemini_api_key');
            toast({
              title: "API Key Removed",
              description: "Your API key has been cleared.",
            });
          }
          setShowApiDialog(false);
        }}
        currentApiKey={apiKey}
      />
    </div>
  );
};

export default Index;
