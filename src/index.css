@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 30% 96%;
    --foreground: 220 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 15%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 217 91% 70%;

    --secondary: 220 15% 95%;
    --secondary-foreground: 220 20% 25%;

    --muted: 220 15% 95%;
    --muted-foreground: 220 15% 45%;

    --accent: 217 91% 95%;
    --accent-foreground: 217 91% 20%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;

    --info: 217 91% 60%;
    --info-foreground: 0 0% 98%;

    /* Gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(217 91% 60%),
      hsl(260 91% 60%)
    );
    --gradient-subtle: linear-gradient(
      180deg,
      hsl(220 30% 96%),
      hsl(220 20% 92%)
    );
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 15% 98%));

    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(217 91% 60% / 0.2);
    --shadow-glow: 0 0 40px hsl(217 91% 70% / 0.3);
    --shadow-soft: 0 2px 10px hsl(0 0% 0% / 0.05);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 220 20% 25%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 15% 95%;
    --sidebar-accent-foreground: 220 20% 25%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 220 25% 8%;
    --foreground: 220 20% 95%;

    --card: 220 20% 10%;
    --card-foreground: 220 20% 95%;

    --popover: 220 20% 10%;
    --popover-foreground: 220 20% 95%;

    --primary: 217 91% 60%;
    --primary-foreground: 220 25% 8%;
    --primary-glow: 217 91% 70%;

    --secondary: 220 15% 15%;
    --secondary-foreground: 220 20% 80%;

    --muted: 220 15% 15%;
    --muted-foreground: 220 15% 60%;

    --accent: 220 15% 15%;
    --accent-foreground: 220 20% 80%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 220 20% 95%;

    --border: 220 15% 20%;
    --input: 220 15% 20%;
    --ring: 217 91% 60%;

    --success: 142 76% 36%;
    --success-foreground: 220 20% 95%;

    --warning: 38 92% 50%;
    --warning-foreground: 220 25% 8%;

    --info: 217 91% 60%;
    --info-foreground: 220 25% 8%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(217 91% 60%),
      hsl(260 91% 60%)
    );
    --gradient-subtle: linear-gradient(
      180deg,
      hsl(220 25% 8%),
      hsl(220 20% 12%)
    );
    --gradient-card: linear-gradient(
      145deg,
      hsl(220 20% 10%),
      hsl(220 15% 15%)
    );

    /* Dark mode shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(217 91% 60% / 0.3);
    --shadow-glow: 0 0 40px hsl(217 91% 70% / 0.4);
    --shadow-soft: 0 2px 10px hsl(0 0% 0% / 0.3);

    --sidebar-background: 220 20% 10%;
    --sidebar-foreground: 220 20% 80%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 220 25% 8%;
    --sidebar-accent: 220 15% 15%;
    --sidebar-accent-foreground: 220 20% 80%;
    --sidebar-border: 220 15% 20%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Modern scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Selection styling */
  ::selection {
    background: hsl(var(--primary) / 0.2);
    color: hsl(var(--primary-foreground));
  }

  /* Focus visible improvements */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: calc(var(--radius) * 0.5);
  }
}

@layer utilities {
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* Modern gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-subtle {
    background: var(--gradient-subtle);
  }

  .bg-gradient-card {
    background: var(--gradient-card);
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Modern shadows */
  .shadow-elegant {
    box-shadow: var(--shadow-elegant);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Modern hover effects */
  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  }

  /* Text gradients */
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Modern animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}
