

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 220 30% 96%;
    --foreground: 220 20% 15%;

    --card: 0 0% 100%;
    --card-foreground: 220 20% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 20% 15%;

    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 217 91% 70%;

    --secondary: 220 15% 95%;
    --secondary-foreground: 220 20% 25%;

    --muted: 220 15% 95%;
    --muted-foreground: 220 15% 45%;

    --accent: 217 91% 95%;
    --accent-foreground: 217 91% 20%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    
    --info: 217 91% 60%;
    --info-foreground: 0 0% 98%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(260 91% 60%));
    --gradient-subtle: linear-gradient(180deg, hsl(220 30% 96%), hsl(220 20% 92%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(220 15% 98%));

    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(217 91% 60% / 0.2);
    --shadow-glow: 0 0 40px hsl(217 91% 70% / 0.3);
    --shadow-soft: 0 2px 10px hsl(0 0% 0% / 0.05);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 220 20% 25%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 15% 95%;
    --sidebar-accent-foreground: 220 20% 25%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 220 25% 8%;
    --foreground: 220 20% 95%;

    --card: 220 20% 10%;
    --card-foreground: 220 20% 95%;

    --popover: 220 20% 10%;
    --popover-foreground: 220 20% 95%;

    --primary: 217 91% 60%;
    --primary-foreground: 220 25% 8%;
    --primary-glow: 217 91% 70%;

    --secondary: 220 15% 15%;
    --secondary-foreground: 220 20% 80%;

    --muted: 220 15% 15%;
    --muted-foreground: 220 15% 60%;

    --accent: 220 15% 15%;
    --accent-foreground: 220 20% 80%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 220 20% 95%;

    --border: 220 15% 20%;
    --input: 220 15% 20%;
    --ring: 217 91% 60%;

    --success: 142 76% 36%;
    --success-foreground: 220 20% 95%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 220 25% 8%;
    
    --info: 217 91% 60%;
    --info-foreground: 220 25% 8%;

    /* Dark mode gradients */
    --gradient-primary: linear-gradient(135deg, hsl(217 91% 60%), hsl(260 91% 60%));
    --gradient-subtle: linear-gradient(180deg, hsl(220 25% 8%), hsl(220 20% 12%));
    --gradient-card: linear-gradient(145deg, hsl(220 20% 10%), hsl(220 15% 15%));

    /* Dark mode shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(217 91% 60% / 0.3);
    --shadow-glow: 0 0 40px hsl(217 91% 70% / 0.4);
    --shadow-soft: 0 2px 10px hsl(0 0% 0% / 0.3);

    --sidebar-background: 220 20% 10%;
    --sidebar-foreground: 220 20% 80%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 220 25% 8%;
    --sidebar-accent: 220 15% 15%;
    --sidebar-accent-foreground: 220 20% 80%;
    --sidebar-border: 220 15% 20%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }
  
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}

