import { Heart, Github, ExternalLink } from "lucide-react";

export function UnifiedFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative mt-20 py-12 bg-gradient-card border-t border-border/50 backdrop-blur-xl">
      {/* Decorative gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 opacity-50"></div>

      <div className="relative container mx-auto px-4">
        <div className="text-center space-y-6">
          {/* Main footer content */}
          <div className="flex items-center justify-center space-x-3 text-lg">
            <span className="text-muted-foreground">Developed with</span>
            <div className="relative">
              <Heart className="h-5 w-5 text-red-500 fill-current animate-pulse" />
              <div className="absolute inset-0 h-5 w-5 text-red-500/30 fill-current animate-ping"></div>
            </div>
            <span className="text-muted-foreground">by</span>
            <a
              href="https://github.com/alindevx00x/"
              target="_blank"
              rel="noopener noreferrer"
              className="group inline-flex items-center space-x-2 font-semibold text-gradient-primary hover:shadow-glow transition-all duration-300 px-3 py-1 rounded-lg hover:bg-primary/10"
            >
              <span>Alindevx00x</span>
              <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
            </a>
            <span className="text-muted-foreground">•</span>
            <span className="font-medium text-foreground">{currentYear}</span>
          </div>

          {/* Additional info */}
          <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>6 AI Providers</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>75+ AI Models</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              <span>4 Professional Tools</span>
            </div>
          </div>

          {/* GitHub link */}
          <div className="pt-4 border-t border-border/30">
            <a
              href="https://github.com/alindevx00x/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-2 text-sm text-muted-foreground hover:text-primary transition-colors group"
            >
              <Github className="h-4 w-4 group-hover:rotate-12 transition-transform" />
              <span>View on GitHub</span>
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}
