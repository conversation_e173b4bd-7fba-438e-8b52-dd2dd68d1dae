import { Heart } from "lucide-react";

export function UnifiedFooter() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="mt-16 py-8 border-t border-border bg-card/30 backdrop-blur-sm">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
          <span>Developed with</span>
          <Heart className="h-4 w-4 text-red-500 fill-current" />
          <span>by</span>
          <a 
            href="https://github.com/alindevx00x/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="font-medium text-primary hover:underline transition-colors"
          >
            Alindevx00x
          </a>
          <span>•</span>
          <span>{currentYear}</span>
        </div>
      </div>
    </footer>
  );
}
