import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Copy, Sparkles, Eye, Settings, Globe, Twitter, Linkedin } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ApiConfig } from "./ApiConfigDialog";
import { createAIService } from "@/services/aiService";

interface BioData {
  niche: string;
  tone: string;
}

interface GeneratedBio {
  name: string;
  tagline: string;
  shortBio: string;
  mediumBio: string;
  longBio: string;
  credibility: {
    education: string;
    publications: string[];
  };
  connect: {
    website: string;
    twitter: string;
    linkedin: string;
  };
  callToAction: string;
  aiImagePrompt: string;
}

interface AuthorBioBuilderProps {
  apiConfig?: ApiConfig | null;
  onApiKeyRequest?: () => void;
}

export function AuthorBioBuilder({ apiConfig, onApiKeyRequest }: AuthorBioBuilderProps) {
  const [bioData, setBioData] = useState<BioData>({
    niche: "",
    tone: ""
  });

  const [generatedBio, setGeneratedBio] = useState<GeneratedBio | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: keyof BioData, value: string) => {
    setBioData(prev => ({ ...prev, [field]: value }));
  };

  const generateWithAI = async (prompt: string): Promise<string> => {
    if (!apiConfig) {
      throw new Error('No API configuration available');
    }

    const aiService = createAIService(apiConfig);
    return await aiService.generateContent(prompt);
  };

  const generateBio = async () => {
    if (!apiConfig) {
      if (onApiKeyRequest) {
        onApiKeyRequest();
      }
      return;
    }

    if (!bioData.niche) {
      toast({
        title: "Missing Information",
        description: "Please provide a website niche/topic.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      const prompt = `Create a comprehensive author bio for a content creator/SEO expert with the following requirements:

Niche/Topic: ${bioData.niche}
Tone: ${bioData.tone || 'Professional and approachable'}

Please generate ALL the following information randomly and realistically:
1. A realistic full name (first and last name)
2. A compelling tagline (one sentence)
3. Short bio (50-75 words)
4. Medium bio (100-150 words)
5. Long bio (200-250 words)
6. Realistic age (between 28-55)
7. Realistic origin/location
8. Realistic family situation
9. Realistic education background relevant to the niche
10. 3-4 realistic publications/awards relevant to the niche
11. Realistic website URL
12. Realistic social media handles
13. Engaging call to action
14. Professional headshot description for AI image generation

Make everything coherent and realistic. The person should be an expert in the specified niche.

Format the response as JSON with the following structure:
{
  "name": "Realistic full name",
  "tagline": "Compelling one-line description",
  "shortBio": "50-75 word bio",
  "mediumBio": "100-150 word bio",
  "longBio": "200-250 word bio",
  "credibility": {
    "education": "Realistic education details relevant to niche",
    "publications": ["Realistic Publication 1", "Realistic Publication 2", "Realistic Publication 3", "Realistic Award/Recognition"]
  },
  "connect": {
    "website": "https://www.realistic-website-name.com",
    "twitter": "https://www.twitter.com/realistic_handle",
    "linkedin": "https://www.linkedin.com/in/realistic-profile"
  },
  "callToAction": "Engaging call to action relevant to the niche",
  "aiImagePrompt": "Detailed prompt for AI image generation of professional headshot including age, appearance, clothing, and background relevant to the niche"
}

Make the bio engaging, professional, and tailored to the specified niche and tone. All details should be realistic and coherent.`;

      const response = await generateWithAI(prompt);
      
      // Clean up the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const bioResult = JSON.parse(jsonMatch[0]);
        setGeneratedBio(bioResult);
        
        toast({
          title: "Bio Generated Successfully!",
          description: "Your author bio has been created with multiple length options.",
        });
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error('Error generating bio:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate bio. Please check your API key and try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${type} copied to clipboard.`,
    });
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4 mb-12">
        <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
          AI-Powered Persona Generator
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Create professional author bios and personas with AI-generated expertise, credentials, and social profiles. Just enter your niche and let AI do the rest!
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Generator Controls */}
        <Card className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <span>Generator Controls</span>
            </CardTitle>
            <CardDescription>
              Just tell us your niche and preferred tone - our AI will create everything else!
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">


            {/* Simplified Input - Only 2 Fields */}
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="niche">Website Niche/Topic *</Label>
                <Input
                  id="niche"
                  placeholder="e.g., Healthy Food, Digital Marketing, Personal Finance, Travel, Technology..."
                  value={bioData.niche}
                  onChange={(e) => handleInputChange('niche', e.target.value)}
                  className="text-base"
                />
                <p className="text-sm text-muted-foreground">
                  What is your website or content about? This will help generate relevant expertise and background.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tone">Bio Tone of Voice</Label>
                <Select value={bioData.tone} onValueChange={(value) => handleInputChange('tone', value)}>
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="Choose your preferred writing style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inspirational">Inspirational and Motivational</SelectItem>
                    <SelectItem value="professional">Professional and Authoritative</SelectItem>
                    <SelectItem value="friendly">Friendly and Approachable</SelectItem>
                    <SelectItem value="expert">Expert and Technical</SelectItem>
                    <SelectItem value="casual">Casual and Conversational</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  How do you want to sound in your bio? This affects the writing style and personality.
                </p>
              </div>

              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">AI Magic ✨</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Our AI will automatically generate realistic details including name, age, education,
                      publications, social media handles, and more - all tailored to your niche!
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Button
              onClick={generateBio}
              disabled={isGenerating || !bioData.niche || !apiConfig}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              size="lg"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              {isGenerating ? "Generating Author Bio..." : "Generate Author Bio"}
            </Button>
          </CardContent>
        </Card>

        {/* Generated Bio Preview */}
        {generatedBio && (
          <Card className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-primary" />
                <span>Generated Bio Preview</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Author Name & Tagline */}
              <div className="text-center space-y-2 p-4 bg-primary/10 rounded-lg border border-primary/20">
                <h2 className="text-2xl font-bold text-primary">{generatedBio.name}</h2>
                <p className="text-muted-foreground italic">{generatedBio.tagline}</p>
              </div>

              {/* Bio Lengths */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Badge variant="secondary">Short</Badge>
                  <Badge variant="secondary">Medium</Badge>
                  <Badge variant="secondary">Long</Badge>
                </div>

                {/* Short Bio */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Short Bio (50-75 words)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.shortBio, "Short bio")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg text-sm">
                    {generatedBio.shortBio}
                  </div>
                </div>

                {/* Medium Bio */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Medium Bio (100-150 words)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.mediumBio, "Medium bio")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg text-sm">
                    {generatedBio.mediumBio}
                  </div>
                </div>

                {/* Long Bio */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Long Bio (200-250 words)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.longBio, "Long bio")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg text-sm max-h-40 overflow-y-auto">
                    {generatedBio.longBio}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Credibility Section */}
              <div className="space-y-3">
                <h4 className="font-medium text-foreground">Credibility</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Education:</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.credibility.education, "Education")}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                    {generatedBio.credibility.education}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm">Publications & Awards:</Label>
                  <ul className="space-y-1">
                    {generatedBio.credibility.publications.map((pub, index) => (
                      <li key={index} className="text-sm text-muted-foreground bg-muted/20 p-2 rounded flex items-center justify-between">
                        <span>• {pub}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(pub, "Publication")}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <Separator />

              {/* Connect Section */}
              <div className="space-y-3">
                <h4 className="font-medium text-foreground">Connect</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm flex items-center space-x-2">
                      <Globe className="h-4 w-4" />
                      <span>Website/Blog:</span>
                    </Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.connect.website, "Website")}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                    {generatedBio.connect.website}
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm flex items-center space-x-2">
                    <Twitter className="h-4 w-4" />
                    <span>Twitter:</span>
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.connect.twitter, "Twitter")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                  {generatedBio.connect.twitter}
                </p>
                <div className="flex items-center justify-between">
                  <Label className="text-sm flex items-center space-x-2">
                    <Linkedin className="h-4 w-4" />
                    <span>LinkedIn:</span>
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.connect.linkedin, "LinkedIn")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                  {generatedBio.connect.linkedin}
                </p>
              </div>

              <Separator />

              {/* Call to Action */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="font-medium">Call to Action:</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.callToAction, "Call to Action")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
                  <p className="text-sm font-medium text-primary">
                    {generatedBio.callToAction}
                  </p>
                </div>
              </div>

              <Separator />

              {/* AI Image Prompt */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="font-medium">AI Image Generator Prompt:</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.aiImagePrompt, "AI Image Prompt")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-muted/30 rounded-lg text-sm max-h-32 overflow-y-auto">
                  {generatedBio.aiImagePrompt}
                </div>
              </div>

              {/* Copy Full Bio Text Button */}
              <Button
                onClick={() => {
                  const fullBioText = `${generatedBio.name}\n${generatedBio.tagline}\n\n${generatedBio.longBio}\n\nConnect:\nWebsite/Blog: ${generatedBio.connect.website}\nTwitter: ${generatedBio.connect.twitter}\nLinkedIn: ${generatedBio.connect.linkedin}\n\nCall to Action: ${generatedBio.callToAction}`;
                  copyToClipboard(fullBioText, "Full Bio Text");
                }}
                className="w-full"
                variant="outline"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Full Bio Text
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
