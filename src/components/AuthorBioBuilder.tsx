import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Copy, User, Sparkles, Eye, Settings, Globe, Twitter, Linkedin } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ApiKeyDialog } from "./ApiKeyDialog";

interface BioData {
  name: string;
  niche: string;
  tone: string;
  age: string;
  origin: string;
  family: string;
  education: string;
  publications: string;
  website: string;
  twitter: string;
  linkedin: string;
  callToAction: string;
}

interface GeneratedBio {
  name: string;
  tagline: string;
  shortBio: string;
  mediumBio: string;
  longBio: string;
  credibility: {
    education: string;
    publications: string[];
  };
  connect: {
    website: string;
    twitter: string;
    linkedin: string;
  };
  callToAction: string;
  aiImagePrompt: string;
}

export function AuthorBioBuilder() {
  const [bioData, setBioData] = useState<BioData>({
    name: "",
    niche: "",
    tone: "",
    age: "",
    origin: "",
    family: "",
    education: "",
    publications: "",
    website: "",
    twitter: "",
    linkedin: "",
    callToAction: ""
  });

  const [generatedBio, setGeneratedBio] = useState<GeneratedBio | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showApiDialog, setShowApiDialog] = useState(false);
  const [apiKey, setApiKey] = useState("");
  const { toast } = useToast();

  const handleInputChange = (field: keyof BioData, value: string) => {
    setBioData(prev => ({ ...prev, [field]: value }));
  };

  const callGeminiAPI = async (prompt: string, apiKey: string): Promise<string> => {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      })
    });

    if (!response.ok) {
      throw new Error('Failed to generate bio');
    }

    const data = await response.json();
    return data.candidates[0].content.parts[0].text;
  };

  const generateBio = async () => {
    if (!apiKey) {
      setShowApiDialog(true);
      return;
    }

    if (!bioData.name || !bioData.niche) {
      toast({
        title: "Missing Information",
        description: "Please provide at least a name and niche/topic.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      const prompt = `Create a comprehensive author bio for a content creator/SEO expert with the following details:

Name: ${bioData.name}
Niche/Topic: ${bioData.niche}
Tone: ${bioData.tone || 'Professional and approachable'}
Age: ${bioData.age || 'Not specified'}
Origin: ${bioData.origin || 'Not specified'}
Family: ${bioData.family || 'Not specified'}
Education: ${bioData.education || 'Not specified'}
Publications/Awards: ${bioData.publications || 'Not specified'}
Website: ${bioData.website || 'Not specified'}
Twitter: ${bioData.twitter || 'Not specified'}
LinkedIn: ${bioData.linkedin || 'Not specified'}
Call to Action: ${bioData.callToAction || 'Not specified'}

Please generate:
1. A compelling tagline (one sentence)
2. Short bio (50-75 words)
3. Medium bio (100-150 words)
4. Long bio (200-250 words)
5. Credibility section with education and publications
6. Social media links section
7. A call to action
8. An AI image generation prompt for a professional headshot

Format the response as JSON with the following structure:
{
  "name": "Full name",
  "tagline": "Compelling one-line description",
  "shortBio": "50-75 word bio",
  "mediumBio": "100-150 word bio", 
  "longBio": "200-250 word bio",
  "credibility": {
    "education": "Education details",
    "publications": ["Publication 1", "Publication 2", "Publication 3"]
  },
  "connect": {
    "website": "website URL or description",
    "twitter": "twitter handle or URL",
    "linkedin": "linkedin URL or description"
  },
  "callToAction": "Engaging call to action",
  "aiImagePrompt": "Detailed prompt for AI image generation of professional headshot"
}

Make the bio engaging, professional, and tailored to the specified niche and tone.`;

      const response = await callGeminiAPI(prompt, apiKey);
      
      // Clean up the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const bioResult = JSON.parse(jsonMatch[0]);
        setGeneratedBio(bioResult);
        
        toast({
          title: "Bio Generated Successfully!",
          description: "Your author bio has been created with multiple length options.",
        });
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error('Error generating bio:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate bio. Please check your API key and try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${type} copied to clipboard.`,
    });
  };

  const handleApiKeySaved = (key: string) => {
    setApiKey(key);
    setShowApiDialog(false);
    toast({
      title: "API Key Saved",
      description: "You can now generate author bios.",
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg">
            <User className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Author Bio Builder
            </h1>
            <p className="text-xl text-muted-foreground">Your AI-powered Persona Generator</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Generator Controls */}
        <Card className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <span>Generator Controls</span>
            </CardTitle>
            <CardDescription>
              Fill in your details to generate a professional author bio
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* API Key Section */}
            <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border border-border/30">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <span className="text-sm font-medium">Gemini API Key</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApiDialog(true)}
              >
                {apiKey ? "Update Key" : "Add Key"}
              </Button>
            </div>

            {/* Basic Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name *</Label>
                  <Input
                    id="name"
                    placeholder="Dr. Evelyn Reed"
                    value={bioData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="age">Age</Label>
                  <Input
                    id="age"
                    placeholder="48"
                    value={bioData.age}
                    onChange={(e) => handleInputChange('age', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="niche">Website Niche/Topic *</Label>
                <Input
                  id="niche"
                  placeholder="Healthy Food"
                  value={bioData.niche}
                  onChange={(e) => handleInputChange('niche', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="tone">Bio Tone of Voice</Label>
                <Select value={bioData.tone} onValueChange={(value) => handleInputChange('tone', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select tone" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="inspirational">Inspirational and Motivational</SelectItem>
                    <SelectItem value="professional">Professional and Authoritative</SelectItem>
                    <SelectItem value="friendly">Friendly and Approachable</SelectItem>
                    <SelectItem value="expert">Expert and Technical</SelectItem>
                    <SelectItem value="casual">Casual and Conversational</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="origin">Origin</Label>
                  <Input
                    id="origin"
                    placeholder="Seattle, Washington"
                    value={bioData.origin}
                    onChange={(e) => handleInputChange('origin', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="family">Family</Label>
                  <Input
                    id="family"
                    placeholder="Lives with husband and two children"
                    value={bioData.family}
                    onChange={(e) => handleInputChange('family', e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="education">Education</Label>
                <Input
                  id="education"
                  placeholder="Ph.D. in Nutritional Biochemistry from UC Berkeley"
                  value={bioData.education}
                  onChange={(e) => handleInputChange('education', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="publications">Publications & Awards</Label>
                <Textarea
                  id="publications"
                  placeholder="Featured in 'Nutrition Today' magazine, Recipient of 'Excellence in Nutrition Education' award..."
                  value={bioData.publications}
                  onChange={(e) => handleInputChange('publications', e.target.value)}
                  className="min-h-20"
                />
              </div>

              <Separator />

              {/* Social Links */}
              <div className="space-y-4">
                <h4 className="font-medium text-foreground">Social Media & Contact</h4>
                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label htmlFor="website" className="flex items-center space-x-2">
                      <Globe className="h-4 w-4" />
                      <span>Website/Blog</span>
                    </Label>
                    <Input
                      id="website"
                      placeholder="https://www.drevelynreed.com"
                      value={bioData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twitter" className="flex items-center space-x-2">
                      <Twitter className="h-4 w-4" />
                      <span>Twitter</span>
                    </Label>
                    <Input
                      id="twitter"
                      placeholder="https://www.twitter.com/DrEvelynReed"
                      value={bioData.twitter}
                      onChange={(e) => handleInputChange('twitter', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="linkedin" className="flex items-center space-x-2">
                      <Linkedin className="h-4 w-4" />
                      <span>LinkedIn</span>
                    </Label>
                    <Input
                      id="linkedin"
                      placeholder="https://www.linkedin.com/in/drevelynreed"
                      value={bioData.linkedin}
                      onChange={(e) => handleInputChange('linkedin', e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="callToAction">Call to Action</Label>
                <Textarea
                  id="callToAction"
                  placeholder="Start your journey to vibrant health today! Explore Dr. Reed's recipes and resources..."
                  value={bioData.callToAction}
                  onChange={(e) => handleInputChange('callToAction', e.target.value)}
                  className="min-h-16"
                />
              </div>
            </div>

            <Button
              onClick={generateBio}
              disabled={isGenerating || !bioData.name || !bioData.niche}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              size="lg"
            >
              <Sparkles className="h-5 w-5 mr-2" />
              {isGenerating ? "Generating Author Bio..." : "Generate Author Bio"}
            </Button>
          </CardContent>
        </Card>

        {/* Generated Bio Preview */}
        {generatedBio && (
          <Card className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-primary" />
                <span>Generated Bio Preview</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Author Name & Tagline */}
              <div className="text-center space-y-2 p-4 bg-primary/10 rounded-lg border border-primary/20">
                <h2 className="text-2xl font-bold text-primary">{generatedBio.name}</h2>
                <p className="text-muted-foreground italic">{generatedBio.tagline}</p>
              </div>

              {/* Bio Lengths */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Badge variant="secondary">Short</Badge>
                  <Badge variant="secondary">Medium</Badge>
                  <Badge variant="secondary">Long</Badge>
                </div>

                {/* Short Bio */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Short Bio (50-75 words)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.shortBio, "Short bio")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg text-sm">
                    {generatedBio.shortBio}
                  </div>
                </div>

                {/* Medium Bio */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Medium Bio (100-150 words)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.mediumBio, "Medium bio")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg text-sm">
                    {generatedBio.mediumBio}
                  </div>
                </div>

                {/* Long Bio */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="font-medium">Long Bio (200-250 words)</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.longBio, "Long bio")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 bg-muted/30 rounded-lg text-sm max-h-40 overflow-y-auto">
                    {generatedBio.longBio}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Credibility Section */}
              <div className="space-y-3">
                <h4 className="font-medium text-foreground">Credibility</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Education:</Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.credibility.education, "Education")}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                    {generatedBio.credibility.education}
                  </p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm">Publications & Awards:</Label>
                  <ul className="space-y-1">
                    {generatedBio.credibility.publications.map((pub, index) => (
                      <li key={index} className="text-sm text-muted-foreground bg-muted/20 p-2 rounded flex items-center justify-between">
                        <span>• {pub}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(pub, "Publication")}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <Separator />

              {/* Connect Section */}
              <div className="space-y-3">
                <h4 className="font-medium text-foreground">Connect</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm flex items-center space-x-2">
                      <Globe className="h-4 w-4" />
                      <span>Website/Blog:</span>
                    </Label>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(generatedBio.connect.website, "Website")}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                    {generatedBio.connect.website}
                  </p>
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm flex items-center space-x-2">
                    <Twitter className="h-4 w-4" />
                    <span>Twitter:</span>
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.connect.twitter, "Twitter")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                  {generatedBio.connect.twitter}
                </p>
                <div className="flex items-center justify-between">
                  <Label className="text-sm flex items-center space-x-2">
                    <Linkedin className="h-4 w-4" />
                    <span>LinkedIn:</span>
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.connect.linkedin, "LinkedIn")}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground bg-muted/20 p-2 rounded">
                  {generatedBio.connect.linkedin}
                </p>
              </div>

              <Separator />

              {/* Call to Action */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="font-medium">Call to Action:</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.callToAction, "Call to Action")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
                  <p className="text-sm font-medium text-primary">
                    {generatedBio.callToAction}
                  </p>
                </div>
              </div>

              <Separator />

              {/* AI Image Prompt */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="font-medium">AI Image Generator Prompt:</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBio.aiImagePrompt, "AI Image Prompt")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-muted/30 rounded-lg text-sm max-h-32 overflow-y-auto">
                  {generatedBio.aiImagePrompt}
                </div>
              </div>

              {/* Copy Full Bio Text Button */}
              <Button
                onClick={() => {
                  const fullBioText = `${generatedBio.name}\n${generatedBio.tagline}\n\n${generatedBio.longBio}\n\nConnect:\nWebsite/Blog: ${generatedBio.connect.website}\nTwitter: ${generatedBio.connect.twitter}\nLinkedIn: ${generatedBio.connect.linkedin}\n\nCall to Action: ${generatedBio.callToAction}`;
                  copyToClipboard(fullBioText, "Full Bio Text");
                }}
                className="w-full"
                variant="outline"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Full Bio Text
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      <ApiKeyDialog
        open={showApiDialog}
        onOpenChange={setShowApiDialog}
        onApiKeySaved={handleApiKeySaved}
        currentApiKey={apiKey}
      />
    </div>
  );
}
