import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { KeywordInput, Keyword } from "./KeywordInput";
import { AnalysisResults, AnalysisData } from "./AnalysisResults";
import { DeveloperAttribution } from "./DeveloperAttribution";
import { ApiConfig } from "./ApiConfigDialog";
import { createAIService } from "@/services/aiService";

// Enhanced Gemini API call simulation with keyword-aware content generation
const callGeminiAPI = async (prompt: string, apiKey: string, keywords: Keyword[]): Promise<any> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Analyze keywords to determine niche and generate relevant content
  const keywordTexts = keywords.map(k => k.keyword.toLowerCase());
  
  if (prompt.includes("main topical niche")) {
    return detectNiche(keywordTexts);
  }
  
  // Generate full analysis based on detected niche
  const niche = detectNiche(keywordTexts);
  return generateRelevantAnalysis(niche, keywordTexts);
};

// Detect niche based on keywords
const detectNiche = (keywordTexts: string[]): string => {
  const niches = {
    'coffee': ['coffee', 'espresso', 'latte', 'cappuccino', 'brew', 'roast', 'cafe', 'barista'],
    'fitness': ['workout', 'exercise', 'gym', 'training', 'muscle', 'cardio', 'fitness', 'health'],
    'cooking': ['recipe', 'cook', 'bake', 'food', 'kitchen', 'meal', 'ingredient', 'dish'],
    'technology': ['tech', 'software', 'app', 'digital', 'computer', 'programming', 'code', 'ai'],
    'travel': ['travel', 'vacation', 'hotel', 'flight', 'destination', 'trip', 'tourism', 'visit'],
    'business': ['business', 'entrepreneur', 'startup', 'marketing', 'sales', 'strategy', 'finance'],
    'education': ['learn', 'study', 'course', 'tutorial', 'education', 'school', 'university', 'teach'],
    'fashion': ['fashion', 'style', 'clothing', 'outfit', 'trend', 'designer', 'accessories', 'wear'],
    'home': ['home', 'house', 'interior', 'decoration', 'furniture', 'garden', 'diy', 'design'],
    'automotive': ['car', 'auto', 'vehicle', 'driving', 'repair', 'maintenance', 'parts', 'garage']
  };

  let maxScore = 0;
  let detectedNiche = 'General Content Strategy';

  for (const [niche, keywords] of Object.entries(niches)) {
    const score = keywordTexts.reduce((acc, keyword) => {
      return acc + keywords.filter(k => keyword.includes(k)).length;
    }, 0);

    if (score > maxScore) {
      maxScore = score;
      detectedNiche = niche.charAt(0).toUpperCase() + niche.slice(1) + ' Industry';
    }
  }

  return detectedNiche;
};

// Generate relevant analysis based on niche
const generateRelevantAnalysis = (niche: string, keywordTexts: string[]): any => {
  const nicheTemplates = {
    'Coffee Industry': {
      categories: [
        'Coffee Brewing Techniques',
        'Coffee Bean Origins & Types',
        'Cafe Culture & Reviews',
        'Coffee Equipment & Accessories',
        'Coffee Health & Lifestyle'
      ],
      pillarTemplates: [
        'The Complete Guide to {category}',
        'Master {category}: From Beginner to Expert',
        'Everything You Need to Know About {category}',
        '{category}: Tips, Tricks, and Best Practices'
      ]
    },
    'Fitness Industry': {
      categories: [
        'Strength Training & Muscle Building',
        'Cardio & Endurance Training',
        'Nutrition & Diet Planning',
        'Home Workout Solutions',
        'Recovery & Wellness'
      ],
      pillarTemplates: [
        'The Ultimate {category} Guide',
        'Transform Your Body with {category}',
        '{category}: Science-Based Approach',
        'Master {category} for Maximum Results'
      ]
    },
    'Technology Industry': {
      categories: [
        'Software Development & Programming',
        'Artificial Intelligence & Machine Learning',
        'Cybersecurity & Data Privacy',
        'Mobile App Development',
        'Cloud Computing & DevOps'
      ],
      pillarTemplates: [
        'Complete {category} Handbook',
        'Modern {category}: Best Practices',
        '{category} for Professionals',
        'Advanced {category} Strategies'
      ]
    }
  };

  // Default template for unrecognized niches
  const defaultTemplate = {
    categories: [
      'Industry Fundamentals & Basics',
      'Advanced Techniques & Strategies',
      'Tools & Resources',
      'Trends & Future Outlook',
      'Community & Networking'
    ],
    pillarTemplates: [
      'The Complete Guide to {category}',
      'Master {category}: Expert Insights',
      '{category}: Best Practices & Tips',
      'Advanced {category} Strategies'
    ]
  };

  const template = nicheTemplates[niche as keyof typeof nicheTemplates] || defaultTemplate;
  
  return {
    niche: niche,
    categories: template.categories.map(categoryName => {
      const categorySlug = categoryName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
      return {
        name: categoryName,
        pillar: template.pillarTemplates[Math.floor(Math.random() * template.pillarTemplates.length)].replace('{category}', categoryName),
        seoTitle: `${categoryName} - Complete Guide & Expert Tips 2024`,
        metaDescription: `Everything you need to know about ${categoryName.toLowerCase()}. Expert guides, tips, and comprehensive resources for ${niche.toLowerCase()}.`,
        urlSlug: categorySlug,
        clusters: generateClustersForCategory(categoryName, keywordTexts)
      };
    })
  };
};

// Generate clusters based on category and keywords
const generateClustersForCategory = (categoryName: string, keywordTexts: string[]) => {
  const clusterTypes = ['how-to', 'comparison', 'list', 'trending', 'commercial'];
  const subClusterTypes = ['long-tail', 'question-based', 'local', 'commercial'];
  
  // Filter relevant keywords for this category
  const relevantKeywords = keywordTexts.filter(keyword => 
    keyword.length > 0 && keyword.split(' ').length <= 5
  ).slice(0, 8); // Limit to 8 most relevant

  return relevantKeywords.map((keyword, index) => {
    const clusterTitle = `${keyword.charAt(0).toUpperCase() + keyword.slice(1)}: Complete Guide`;
    const urlSlug = keyword.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');

    return {
      title: clusterTitle,
      type: clusterTypes[index % clusterTypes.length],
      seoTitle: `${clusterTitle} - ${categoryName} Expert Tips 2024`,
      metaDescription: `Discover everything about ${keyword} in our comprehensive guide. Expert tips, best practices, and actionable insights for ${categoryName.toLowerCase()}.`,
      urlSlug: `${urlSlug}-complete-guide`,
      subClusters: [
        {
          title: `Best ${keyword} for beginners`,
          type: subClusterTypes[0],
          seoTitle: `Best ${keyword} for Beginners - Top Recommendations 2024`,
          metaDescription: `Find the best ${keyword} options for beginners. Our expert recommendations and beginner-friendly guide to get started.`,
          urlSlug: `best-${urlSlug}-beginners`
        },
        {
          title: `How to choose ${keyword}`,
          type: subClusterTypes[1],
          seoTitle: `How to Choose ${keyword} - Complete Buyer's Guide`,
          metaDescription: `Learn how to choose the right ${keyword} with our comprehensive buyer's guide. Compare features, prices, and expert recommendations.`,
          urlSlug: `how-to-choose-${urlSlug}`
        },
        {
          title: `${keyword} near me`,
          type: subClusterTypes[2],
          seoTitle: `${keyword} Near Me - Find Local Options & Reviews`,
          metaDescription: `Find the best ${keyword} options near you. Local reviews, ratings, and recommendations for ${keyword} in your area.`,
          urlSlug: `${urlSlug}-near-me`
        }
      ],
      internalLinks: [
        `${categoryName} Overview`,
        `Advanced ${keyword} techniques`,
        `${keyword} troubleshooting guide`
      ],
      externalLinks: [
        "https://example.com/industry-insights",
        "https://example.com/expert-reviews",
        "https://example.com/community-forum"
      ]
    };
  });
};

interface SEOClusterToolProps {
  apiConfig?: ApiConfig | null;
  onApiKeyRequest?: () => void;
}

export function SEOClusterTool({ apiConfig, onApiKeyRequest }: SEOClusterToolProps) {
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);



  const handleAnalyze = async () => {
    if (!apiConfig) {
      toast({
        title: "API Configuration Required",
        description: "Please configure your AI API settings first.",
        variant: "destructive",
      });
      if (onApiKeyRequest) {
        onApiKeyRequest();
      }
      return;
    }

    if (keywords.length === 0) {
      toast({
        title: "No Keywords",
        description: "Please add some keywords to analyze.",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    setAnalysisData(null); // Clear previous results

    try {
      // Step 1: Detect niche
      toast({
        title: "Analysis Started",
        description: "Detecting main niche from your keywords...",
      });

      const keywordList = keywords.map(k => k.keyword).join(', ');
      const niche = await callGeminiAPI(
        `Given this list of keywords, what is the main topical niche or theme? Keywords: ${keywordList}`,
        apiKey,
        keywords
      );

      // Step 2: Generate full analysis
      toast({
        title: "Generating Clusters",
        description: "Creating content clusters and linking strategies...",
      });

      const fullAnalysis = await callGeminiAPI(
        `Generate a complete content cluster analysis for the niche "${niche}" using these keywords: ${keywordList}`,
        apiKey,
        keywords
      );

      setAnalysisData(fullAnalysis);

      toast({
        title: "Analysis Complete",
        description: `Generated ${fullAnalysis.categories.length} content categories with detailed clustering.`,
      });

    } catch (error) {
      console.error('Analysis error:', error);
      toast({
        title: "Analysis Failed",
        description: "There was an error analyzing your keywords. Please check your API key and try again.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleExport = (format: 'csv' | 'json' | 'html') => {
    if (!analysisData) return;

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'csv':
        content = generateCSV(analysisData);
        filename = 'seo-cluster-analysis.csv';
        mimeType = 'text/csv';
        break;
      case 'json':
        content = JSON.stringify(analysisData, null, 2);
        filename = 'seo-cluster-analysis.json';
        mimeType = 'application/json';
        break;
      case 'html':
        content = generateHTML(analysisData);
        filename = 'seo-cluster-analysis.html';
        mimeType = 'text/html';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export Complete",
      description: `Your analysis has been exported as ${filename}`,
    });
  };

  const generateCSV = (data: AnalysisData): string => {
    let csv = 'Category,Pillar,Cluster,Cluster Type,Sub-Cluster,Sub-Cluster Type,Internal Links,External Links\n';
    
    data.categories.forEach(category => {
      category.clusters.forEach(cluster => {
        cluster.subClusters.forEach(subCluster => {
          csv += `"${category.name}","${category.pillar}","${cluster.title}","${cluster.type}","${subCluster.title}","${subCluster.type}","${cluster.internalLinks.join('; ')}","${cluster.externalLinks.join('; ')}"\n`;
        });
      });
    });
    
    return csv;
  };

  const generateHTML = (data: AnalysisData): string => {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Content Cluster Analysis - ${data.niche}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        .category { margin-bottom: 30px; border: 1px solid #ddd; padding: 20px; }
        .pillar { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
        .cluster { margin-left: 20px; margin-bottom: 15px; }
        .sub-cluster { margin-left: 40px; font-size: 14px; color: #666; }
        .links { margin-top: 10px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Content Cluster Analysis</h1>
    <h2>Niche: ${data.niche}</h2>
    ${data.categories.map(category => `
        <div class="category">
            <h3>${category.name}</h3>
            <div class="pillar">
                <strong>Pillar Page:</strong> ${category.pillar}
            </div>
            ${category.clusters.map(cluster => `
                <div class="cluster">
                    <h4>${cluster.title} (${cluster.type})</h4>
                    ${cluster.subClusters.map(sub => `
                        <div class="sub-cluster">• ${sub.title} (${sub.type})</div>
                    `).join('')}
                    <div class="links">
                        <strong>Internal Links:</strong> ${cluster.internalLinks.join(', ')}<br>
                        <strong>External Links:</strong> ${cluster.externalLinks.join(', ')}
                    </div>
                </div>
            `).join('')}
        </div>
    `).join('')}
</body>
</html>`;
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4 mb-12">
        <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
          AI-Powered Content Strategist
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Transform your keyword research into comprehensive content strategies with AI-driven cluster analysis,
          pillar page recommendations, and intelligent linking strategies.
        </p>


      </div>

      <KeywordInput
        keywords={keywords}
        onKeywordsChange={setKeywords}
        onAnalyze={handleAnalyze}
        isLoading={isAnalyzing}
      />

      {analysisData && (
        <AnalysisResults
          analysisData={analysisData}
          onExport={handleExport}
        />
      )}

      <DeveloperAttribution />
    </div>
  );
}
