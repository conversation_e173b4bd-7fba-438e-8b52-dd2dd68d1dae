import { useState, useEffect } from "react";
import { toast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { KeywordInput, Keyword } from "./KeywordInput";
import { AnalysisResults, AnalysisData } from "./AnalysisResults";
import { ApiConfig } from "./ApiConfigDialog";
import { createAIService } from "@/services/aiService";



interface SEOClusterToolProps {
  apiConfig?: ApiConfig | null;
  onApiKeyRequest?: () => void;
}

export function SEOClusterTool({ apiConfig, onApiKeyRequest }: SEOClusterToolProps) {
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);



  const handleAnalyze = async () => {
    if (!apiConfig) {
      toast({
        title: "API Configuration Required",
        description: "Please configure your AI API settings first.",
        variant: "destructive",
      });
      if (onApiKeyRequest) {
        onApiKeyRequest();
      }
      return;
    }

    if (keywords.length === 0) {
      toast({
        title: "No Keywords",
        description: "Please add some keywords to analyze.",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    setAnalysisData(null); // Clear previous results

    try {
      // Use AI service to generate analysis
      const aiService = createAIService(apiConfig);

      toast({
        title: "Analysis Started",
        description: `Analyzing keywords with ${aiService.getProviderName()}...`,
      });

      const keywordList = keywords.map(k => k.keyword).join(', ');
      const prompt = `Analyze the following keywords for SEO content clustering: ${keywordList}

Please create a comprehensive SEO content strategy with the following structure:

1. Identify the main niche/topic from these keywords
2. Create 3-5 content categories that group related keywords
3. For each category, create:
   - A pillar page topic
   - 3-4 content clusters with specific topics
   - Each cluster should have 2-3 sub-clusters
   - SEO recommendations (title, meta description, URL slug) for categories, clusters, and sub-clusters
   - Internal and external linking suggestions

Format the response as JSON with this exact structure:
{
  "niche": "Main topic/niche",
  "categories": [
    {
      "name": "Category Name",
      "pillar": "Pillar page topic",
      "seoTitle": "SEO optimized title",
      "metaDescription": "150-160 character meta description",
      "urlSlug": "category-url-slug",
      "clusters": [
        {
          "title": "Cluster topic",
          "type": "how-to",
          "seoTitle": "SEO title for cluster",
          "metaDescription": "Meta description for cluster",
          "urlSlug": "cluster-url-slug",
          "subClusters": [
            {
              "title": "Sub-cluster topic",
              "type": "long-tail",
              "seoTitle": "SEO title for sub-cluster",
              "metaDescription": "Meta description for sub-cluster",
              "urlSlug": "sub-cluster-url-slug"
            }
          ],
          "internalLinks": ["Link suggestion 1", "Link suggestion 2"],
          "externalLinks": ["https://example1.com", "https://example2.com"]
        }
      ]
    }
  ]
}

Make sure all content is relevant to the provided keywords and optimized for SEO.`;

      const response = await aiService.generateContent(prompt);

      // Clean up the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const analysisResult = JSON.parse(jsonMatch[0]);
        setAnalysisData(analysisResult);

        toast({
          title: "Analysis Complete!",
          description: `Generated ${analysisResult.categories.length} content categories with detailed cluster analysis using ${aiService.getProviderName()}.`,
        });
      } else {
        throw new Error("Invalid response format from AI service");
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "There was an error analyzing your keywords. Please check your API configuration and try again.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleExport = (format: 'csv' | 'json' | 'html') => {
    if (!analysisData) return;

    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'csv':
        content = generateCSV(analysisData);
        filename = 'seo-cluster-analysis.csv';
        mimeType = 'text/csv';
        break;
      case 'json':
        content = JSON.stringify(analysisData, null, 2);
        filename = 'seo-cluster-analysis.json';
        mimeType = 'application/json';
        break;
      case 'html':
        content = generateHTML(analysisData);
        filename = 'seo-cluster-analysis.html';
        mimeType = 'text/html';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export Complete",
      description: `Your analysis has been exported as ${filename}`,
    });
  };

  const generateCSV = (data: AnalysisData): string => {
    let csv = 'Category,Pillar,Cluster,Cluster Type,Sub-Cluster,Sub-Cluster Type,Internal Links,External Links\n';
    
    data.categories.forEach(category => {
      category.clusters.forEach(cluster => {
        cluster.subClusters.forEach(subCluster => {
          csv += `"${category.name}","${category.pillar}","${cluster.title}","${cluster.type}","${subCluster.title}","${subCluster.type}","${cluster.internalLinks.join('; ')}","${cluster.externalLinks.join('; ')}"\n`;
        });
      });
    });
    
    return csv;
  };

  const generateHTML = (data: AnalysisData): string => {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Content Cluster Analysis - ${data.niche}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        .category { margin-bottom: 30px; border: 1px solid #ddd; padding: 20px; }
        .pillar { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
        .cluster { margin-left: 20px; margin-bottom: 15px; }
        .sub-cluster { margin-left: 40px; font-size: 14px; color: #666; }
        .links { margin-top: 10px; font-size: 12px; }
    </style>
</head>
<body>
    <h1>Content Cluster Analysis</h1>
    <h2>Niche: ${data.niche}</h2>
    ${data.categories.map(category => `
        <div class="category">
            <h3>${category.name}</h3>
            <div class="pillar">
                <strong>Pillar Page:</strong> ${category.pillar}
            </div>
            ${category.clusters.map(cluster => `
                <div class="cluster">
                    <h4>${cluster.title} (${cluster.type})</h4>
                    ${cluster.subClusters.map(sub => `
                        <div class="sub-cluster">• ${sub.title} (${sub.type})</div>
                    `).join('')}
                    <div class="links">
                        <strong>Internal Links:</strong> ${cluster.internalLinks.join(', ')}<br>
                        <strong>External Links:</strong> ${cluster.externalLinks.join(', ')}
                    </div>
                </div>
            `).join('')}
        </div>
    `).join('')}
</body>
</html>`;
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="text-center space-y-4 mb-12">
        <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
          AI-Powered Content Strategist
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Transform your keyword research into comprehensive content strategies with AI-driven cluster analysis,
          pillar page recommendations, and intelligent linking strategies.
        </p>


      </div>

      <KeywordInput
        keywords={keywords}
        onKeywordsChange={setKeywords}
        onAnalyze={handleAnalyze}
        isLoading={isAnalyzing}
      />

      {analysisData && (
        <AnalysisResults
          analysisData={analysisData}
          onExport={handleExport}
        />
      )}
    </div>
  );
}
