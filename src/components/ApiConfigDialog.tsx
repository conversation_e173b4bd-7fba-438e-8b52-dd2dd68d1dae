import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, EyeOff, Key, Zap, Brain, Sparkles, Bot, Cpu } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export interface ApiProvider {
  id: string;
  name: string;
  icon: React.ReactNode;
  models: { id: string; name: string; description: string }[];
  keyPlaceholder: string;
  website: string;
}

export interface ApiConfig {
  provider: string;
  model: string;
  apiKey: string;
}

const API_PROVIDERS: ApiProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    icon: <Brain className="h-5 w-5" />,
    models: [
      { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model, best for complex tasks' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster and more affordable' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'High performance with large context' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and cost-effective' }
    ],
    keyPlaceholder: 'sk-...',
    website: 'https://platform.openai.com/api-keys'
  },
  {
    id: 'anthropic',
    name: 'Claude (Anthropic)',
    icon: <Sparkles className="h-5 w-5" />,
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet', description: 'Most intelligent model, best for complex tasks' },
      { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku', description: 'Fastest model, great for simple tasks' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful model for difficult tasks' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance and speed' }
    ],
    keyPlaceholder: 'sk-ant-...',
    website: 'https://console.anthropic.com/settings/keys'
  },
  {
    id: 'google',
    name: 'Google Gemini',
    icon: <Zap className="h-5 w-5" />,
    models: [
      { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro', description: 'Most capable model with large context' },
      { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash', description: 'Fast and efficient for most tasks' },
      { id: 'gemini-pro', name: 'Gemini Pro', description: 'Reliable performance for general use' }
    ],
    keyPlaceholder: 'AIza...',
    website: 'https://aistudio.google.com/app/apikey'
  },
  {
    id: 'xai',
    name: 'Grok (xAI)',
    icon: <Bot className="h-5 w-5" />,
    models: [
      { id: 'grok-beta', name: 'Grok Beta', description: 'Latest Grok model with real-time data' },
      { id: 'grok-vision-beta', name: 'Grok Vision Beta', description: 'Grok with vision capabilities' }
    ],
    keyPlaceholder: 'xai-...',
    website: 'https://console.x.ai/team/api-keys'
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    icon: <Cpu className="h-5 w-5" />,
    models: [
      { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'General purpose conversational AI' },
      { id: 'deepseek-coder', name: 'DeepSeek Coder', description: 'Specialized for coding tasks' }
    ],
    keyPlaceholder: 'sk-...',
    website: 'https://platform.deepseek.com/api_keys'
  }
];

interface ApiConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfigSaved: (config: ApiConfig) => void;
  currentConfig?: ApiConfig;
}

export function ApiConfigDialog({ open, onOpenChange, onConfigSaved, currentConfig }: ApiConfigDialogProps) {
  const [selectedProvider, setSelectedProvider] = useState(currentConfig?.provider || 'openai');
  const [selectedModel, setSelectedModel] = useState(currentConfig?.model || '');
  const [apiKey, setApiKey] = useState(currentConfig?.apiKey || '');
  const [showApiKey, setShowApiKey] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (currentConfig) {
      setSelectedProvider(currentConfig.provider);
      setSelectedModel(currentConfig.model);
      setApiKey(currentConfig.apiKey);
    }
  }, [currentConfig]);

  const handleProviderChange = (providerId: string) => {
    setSelectedProvider(providerId);
    setSelectedModel(''); // Reset model when provider changes
    setApiKey(''); // Reset API key when provider changes
  };

  const handleSave = () => {
    if (!selectedProvider || !selectedModel || !apiKey) {
      toast({
        title: "Missing Information",
        description: "Please select a provider, model, and enter your API key.",
        variant: "destructive"
      });
      return;
    }

    const config: ApiConfig = {
      provider: selectedProvider,
      model: selectedModel,
      apiKey: apiKey.trim()
    };

    onConfigSaved(config);
    onOpenChange(false);
    
    toast({
      title: "API Configuration Saved",
      description: `Now using ${API_PROVIDERS.find(p => p.id === selectedProvider)?.name} with ${API_PROVIDERS.find(p => p.id === selectedProvider)?.models.find(m => m.id === selectedModel)?.name}`,
    });
  };

  const selectedProviderData = API_PROVIDERS.find(p => p.id === selectedProvider);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>API Configuration</span>
          </DialogTitle>
          <DialogDescription>
            Choose your preferred AI provider and configure your API settings
          </DialogDescription>
        </DialogHeader>

        <Tabs value={selectedProvider} onValueChange={handleProviderChange} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            {API_PROVIDERS.map((provider) => (
              <TabsTrigger key={provider.id} value={provider.id} className="flex items-center space-x-2">
                {provider.icon}
                <span className="hidden sm:inline">{provider.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {API_PROVIDERS.map((provider) => (
            <TabsContent key={provider.id} value={provider.id} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {provider.icon}
                    <span>{provider.name}</span>
                    <Badge variant="secondary">
                      {provider.models.length} models
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    Configure your {provider.name} API settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Model Selection */}
                  <div className="space-y-2">
                    <Label htmlFor={`model-${provider.id}`}>Select Model</Label>
                    <Select 
                      value={selectedProvider === provider.id ? selectedModel : ''} 
                      onValueChange={setSelectedModel}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a model" />
                      </SelectTrigger>
                      <SelectContent>
                        {provider.models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{model.name}</span>
                              <span className="text-xs text-muted-foreground">{model.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* API Key Input */}
                  <div className="space-y-2">
                    <Label htmlFor={`apikey-${provider.id}`}>API Key</Label>
                    <div className="relative">
                      <Input
                        id={`apikey-${provider.id}`}
                        type={showApiKey ? "text" : "password"}
                        placeholder={provider.keyPlaceholder}
                        value={selectedProvider === provider.id ? apiKey : ''}
                        onChange={(e) => setApiKey(e.target.value)}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Get your API key from{' '}
                      <a 
                        href={provider.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {provider.name} Dashboard
                      </a>
                    </p>
                  </div>

                  {/* Model Details */}
                  {selectedModel && selectedProvider === provider.id && (
                    <div className="p-3 bg-muted/30 rounded-lg border">
                      <h4 className="font-medium mb-2">Selected Model Details</h4>
                      {(() => {
                        const model = provider.models.find(m => m.id === selectedModel);
                        return model ? (
                          <div>
                            <p className="text-sm font-medium">{model.name}</p>
                            <p className="text-xs text-muted-foreground">{model.description}</p>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export the API providers for use in other components
export { API_PROVIDERS };
