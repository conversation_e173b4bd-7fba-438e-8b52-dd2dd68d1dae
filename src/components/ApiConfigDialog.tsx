import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, EyeOff, Key, Zap, Brain, Sparkles, Bot, Cpu, Network, ExternalLink } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export interface ApiProvider {
  id: string;
  name: string;
  icon: React.ReactNode;
  models: { id: string; name: string; description: string }[];
  keyPlaceholder: string;
  website: string;
}

export interface ApiConfig {
  provider: string;
  model: string;
  apiKey: string;
}

const API_PROVIDERS: ApiProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    icon: <Brain className="h-5 w-5" />,
    models: [
      { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model, multimodal, 128K context' },
      { id: 'gpt-4o-2024-11-20', name: 'GPT-4o (Nov 2024)', description: 'Latest GPT-4o with improved capabilities' },
      { id: 'gpt-4o-2024-08-06', name: 'GPT-4o (Aug 2024)', description: 'Structured outputs, function calling' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster, cheaper, 128K context' },
      { id: 'gpt-4o-mini-2024-07-18', name: 'GPT-4o Mini (July 2024)', description: 'Cost-effective with high intelligence' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'High performance, 128K context, vision' },
      { id: 'gpt-4-turbo-2024-04-09', name: 'GPT-4 Turbo (April 2024)', description: 'Latest GPT-4 Turbo with vision' },
      { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo Preview', description: 'Preview version with latest features' },
      { id: 'gpt-4', name: 'GPT-4', description: 'Original GPT-4, 8K context' },
      { id: 'gpt-4-0613', name: 'GPT-4 (June 2023)', description: 'Function calling, improved instruction following' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast, cost-effective, 16K context' },
      { id: 'gpt-3.5-turbo-0125', name: 'GPT-3.5 Turbo (Jan 2024)', description: 'Latest 3.5 with reduced costs' },
      { id: 'gpt-3.5-turbo-1106', name: 'GPT-3.5 Turbo (Nov 2023)', description: 'Improved instruction following' }
    ],
    keyPlaceholder: 'sk-proj-...',
    website: 'https://platform.openai.com/api-keys'
  },
  {
    id: 'anthropic',
    name: 'Claude (Anthropic)',
    icon: <Sparkles className="h-5 w-5" />,
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet (Oct 2024)', description: 'Most intelligent, computer use, 200K context' },
      { id: 'claude-3-5-sonnet-20240620', name: 'Claude 3.5 Sonnet (June 2024)', description: 'Balanced intelligence and speed' },
      { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku (Oct 2024)', description: 'Fastest model, great for simple tasks' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful, best for complex tasks' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance and cost' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: 'Fastest and most compact model' },
      { id: 'claude-2.1', name: 'Claude 2.1', description: 'Previous generation, 200K context' },
      { id: 'claude-2.0', name: 'Claude 2.0', description: 'Earlier version, good for general tasks' },
      { id: 'claude-instant-1.2', name: 'Claude Instant 1.2', description: 'Fast and affordable option' }
    ],
    keyPlaceholder: 'sk-ant-...',
    website: 'https://console.anthropic.com/settings/keys'
  },
  {
    id: 'google',
    name: 'Google Gemini',
    icon: <Zap className="h-5 w-5" />,
    models: [
      { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash (Experimental)', description: 'Latest generation, multimodal, real-time capabilities' },
      { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro (Latest)', description: 'Most capable, 2M context, multimodal' },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'High-performance model with large context' },
      { id: 'gemini-1.5-pro-002', name: 'Gemini 1.5 Pro (002)', description: 'Enhanced reasoning and code generation' },
      { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash (Latest)', description: 'Fast, efficient, 1M context' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Optimized for speed and efficiency' },
      { id: 'gemini-1.5-flash-002', name: 'Gemini 1.5 Flash (002)', description: 'Improved performance and accuracy' },
      { id: 'gemini-1.5-flash-8b-latest', name: 'Gemini 1.5 Flash 8B', description: 'Smaller, faster variant' },
      { id: 'gemini-1.0-pro-latest', name: 'Gemini 1.0 Pro (Latest)', description: 'Stable version for production' },
      { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Original Pro model' },
      { id: 'gemini-pro', name: 'Gemini Pro', description: 'Legacy naming for Gemini 1.0 Pro' },
      { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', description: 'Multimodal with vision capabilities' }
    ],
    keyPlaceholder: 'AIza...',
    website: 'https://aistudio.google.com/app/apikey'
  },
  {
    id: 'xai',
    name: 'Grok (xAI)',
    icon: <Bot className="h-5 w-5" />,
    models: [
      { id: 'grok-beta', name: 'Grok Beta', description: 'Latest Grok with real-time X data access' },
      { id: 'grok-vision-beta', name: 'Grok Vision Beta', description: 'Multimodal Grok with image understanding' },
      { id: 'grok-2-latest', name: 'Grok-2 (Latest)', description: 'Most advanced Grok model' },
      { id: 'grok-2-1212', name: 'Grok-2 (Dec 2024)', description: 'Enhanced reasoning and knowledge' },
      { id: 'grok-2-vision-1212', name: 'Grok-2 Vision (Dec 2024)', description: 'Advanced vision capabilities' },
      { id: 'grok-2-mini', name: 'Grok-2 Mini', description: 'Smaller, faster version of Grok-2' },
      { id: 'grok-3-beta', name: 'Grok-3 Beta', description: 'Next generation Grok with enhanced capabilities' }
    ],
    keyPlaceholder: 'xai-...',
    website: 'https://console.x.ai/team/api-keys'
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    icon: <Cpu className="h-5 w-5" />,
    models: [
      { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'General purpose conversational AI' },
      { id: 'deepseek-coder', name: 'DeepSeek Coder', description: 'Specialized for programming tasks' },
      { id: 'deepseek-v3', name: 'DeepSeek V3', description: 'Latest generation with enhanced reasoning' },
      { id: 'deepseek-v2.5', name: 'DeepSeek V2.5', description: 'Improved capabilities and performance' },
      { id: 'deepseek-v2', name: 'DeepSeek V2', description: 'Advanced reasoning and knowledge' },
      { id: 'deepseek-coder-v2', name: 'DeepSeek Coder V2', description: 'Enhanced coding capabilities' },
      { id: 'deepseek-math', name: 'DeepSeek Math', description: 'Specialized for mathematical reasoning' },
      { id: 'deepseek-reasoning', name: 'DeepSeek Reasoning', description: 'Optimized for logical reasoning tasks' },
      { id: 'deepseek-r1', name: 'DeepSeek R1', description: 'Reasoning-focused model with chain-of-thought' }
    ],
    keyPlaceholder: 'sk-...',
    website: 'https://platform.deepseek.com/api_keys'
  },
  {
    id: 'openrouter',
    name: 'OpenRouter',
    icon: <Network className="h-5 w-5" />,
    models: [
      // OpenAI Models via OpenRouter
      { id: 'openai/gpt-4o', name: 'GPT-4o (OpenRouter)', description: 'OpenAI GPT-4o via OpenRouter' },
      { id: 'openai/gpt-4o-mini', name: 'GPT-4o Mini (OpenRouter)', description: 'OpenAI GPT-4o Mini via OpenRouter' },
      { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo (OpenRouter)', description: 'OpenAI GPT-4 Turbo via OpenRouter' },
      { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo (OpenRouter)', description: 'OpenAI GPT-3.5 Turbo via OpenRouter' },

      // Anthropic Models via OpenRouter
      { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet (OpenRouter)', description: 'Anthropic Claude 3.5 Sonnet via OpenRouter' },
      { id: 'anthropic/claude-3-opus', name: 'Claude 3 Opus (OpenRouter)', description: 'Anthropic Claude 3 Opus via OpenRouter' },
      { id: 'anthropic/claude-3-sonnet', name: 'Claude 3 Sonnet (OpenRouter)', description: 'Anthropic Claude 3 Sonnet via OpenRouter' },
      { id: 'anthropic/claude-3-haiku', name: 'Claude 3 Haiku (OpenRouter)', description: 'Anthropic Claude 3 Haiku via OpenRouter' },

      // Google Models via OpenRouter
      { id: 'google/gemini-pro-1.5', name: 'Gemini Pro 1.5 (OpenRouter)', description: 'Google Gemini Pro 1.5 via OpenRouter' },
      { id: 'google/gemini-flash-1.5', name: 'Gemini Flash 1.5 (OpenRouter)', description: 'Google Gemini Flash 1.5 via OpenRouter' },

      // Meta Models via OpenRouter
      { id: 'meta-llama/llama-3.1-405b-instruct', name: 'Llama 3.1 405B Instruct', description: 'Meta Llama 3.1 405B, most capable open model' },
      { id: 'meta-llama/llama-3.1-70b-instruct', name: 'Llama 3.1 70B Instruct', description: 'Meta Llama 3.1 70B, balanced performance' },
      { id: 'meta-llama/llama-3.1-8b-instruct', name: 'Llama 3.1 8B Instruct', description: 'Meta Llama 3.1 8B, fast and efficient' },

      // Mistral Models via OpenRouter
      { id: 'mistralai/mistral-large', name: 'Mistral Large', description: 'Mistral AI Large model, high performance' },
      { id: 'mistralai/mistral-medium', name: 'Mistral Medium', description: 'Mistral AI Medium model, balanced' },
      { id: 'mistralai/mistral-small', name: 'Mistral Small', description: 'Mistral AI Small model, fast and cost-effective' },

      // Cohere Models via OpenRouter
      { id: 'cohere/command-r-plus', name: 'Command R+', description: 'Cohere Command R+, optimized for RAG and tool use' },
      { id: 'cohere/command-r', name: 'Command R', description: 'Cohere Command R, balanced performance' },

      // Perplexity Models via OpenRouter
      { id: 'perplexity/llama-3.1-sonar-large-128k-online', name: 'Sonar Large Online', description: 'Perplexity Sonar with real-time web access' },
      { id: 'perplexity/llama-3.1-sonar-small-128k-online', name: 'Sonar Small Online', description: 'Perplexity Sonar small with web access' },

      // Other Popular Models
      { id: 'qwen/qwen-2.5-72b-instruct', name: 'Qwen 2.5 72B Instruct', description: 'Alibaba Qwen 2.5 72B, multilingual capabilities' },
      { id: 'deepseek/deepseek-chat', name: 'DeepSeek Chat (OpenRouter)', description: 'DeepSeek Chat via OpenRouter' },
      { id: 'x-ai/grok-beta', name: 'Grok Beta (OpenRouter)', description: 'xAI Grok Beta via OpenRouter' }
    ],
    keyPlaceholder: 'sk-or-v1-...',
    website: 'https://openrouter.ai/keys'
  }
];

interface ApiConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfigSaved: (config: ApiConfig) => void;
  currentConfig?: ApiConfig;
}

export function ApiConfigDialog({ open, onOpenChange, onConfigSaved, currentConfig }: ApiConfigDialogProps) {
  const [selectedProvider, setSelectedProvider] = useState(currentConfig?.provider || 'openai');
  const [selectedModel, setSelectedModel] = useState(currentConfig?.model || '');
  const [apiKey, setApiKey] = useState(currentConfig?.apiKey || '');
  const [showApiKey, setShowApiKey] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (currentConfig) {
      setSelectedProvider(currentConfig.provider);
      setSelectedModel(currentConfig.model);
      setApiKey(currentConfig.apiKey);
    }
  }, [currentConfig]);

  const handleProviderChange = (providerId: string) => {
    setSelectedProvider(providerId);
    setSelectedModel(''); // Reset model when provider changes
    setApiKey(''); // Reset API key when provider changes
  };

  const handleSave = () => {
    if (!selectedProvider || !selectedModel || !apiKey) {
      toast({
        title: "Missing Information",
        description: "Please select a provider, model, and enter your API key.",
        variant: "destructive"
      });
      return;
    }

    const config: ApiConfig = {
      provider: selectedProvider,
      model: selectedModel,
      apiKey: apiKey.trim()
    };

    onConfigSaved(config);
    onOpenChange(false);
    
    toast({
      title: "API Configuration Saved",
      description: `Now using ${API_PROVIDERS.find(p => p.id === selectedProvider)?.name} with ${API_PROVIDERS.find(p => p.id === selectedProvider)?.models.find(m => m.id === selectedModel)?.name}`,
    });
  };

  const selectedProviderData = API_PROVIDERS.find(p => p.id === selectedProvider);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto bg-gradient-to-br from-background via-background to-muted/20 border-2 border-primary/20 shadow-2xl">
        <DialogHeader className="text-center pb-10">
          <div className="relative mx-auto mb-8">
            <div className="absolute inset-0 w-24 h-24 bg-gradient-primary rounded-3xl blur-lg opacity-30"></div>
            <div className="relative w-24 h-24 bg-gradient-primary rounded-3xl flex items-center justify-center shadow-glow">
              <Key className="h-12 w-12 text-white" />
            </div>
          </div>
          <DialogTitle className="text-4xl font-bold text-center bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
            AI API Configuration
          </DialogTitle>
          <DialogDescription className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed text-center">
            Choose your preferred AI provider and configure your API settings to unlock the full potential of our professional SEO content suite.
          </DialogDescription>
          <div className="flex flex-wrap justify-center gap-3 mt-6">
            <Badge variant="outline" className="px-4 py-2 text-sm font-medium">6 AI Providers</Badge>
            <Badge variant="outline" className="px-4 py-2 text-sm font-medium">75+ Models</Badge>
            <Badge variant="outline" className="px-4 py-2 text-sm font-medium">Professional Tools</Badge>
          </div>
        </DialogHeader>

        <Tabs value={selectedProvider} onValueChange={handleProviderChange} className="w-full">
          {/* Enhanced Provider Selection */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-center mb-8 text-foreground">
              Select Your AI Provider
            </h3>
            <TabsList className="grid w-full grid-cols-6 gap-4 p-6 bg-gradient-to-r from-muted/30 via-muted/50 to-muted/30 rounded-3xl border border-border/50 shadow-inner">
              {API_PROVIDERS.map((provider) => {
                const isSelected = selectedProvider === provider.id;
                const isConfigured = currentConfig?.provider === provider.id;
                return (
                  <TabsTrigger
                    key={provider.id}
                    value={provider.id}
                    className={`relative flex flex-col items-center justify-center space-y-2 px-4 py-6 rounded-2xl transition-all duration-300 hover-lift group ${
                      isSelected
                        ? 'bg-gradient-primary text-white shadow-glow scale-105'
                        : isConfigured
                          ? 'bg-gradient-to-br from-green-100 to-green-50 dark:from-green-900/30 dark:to-green-800/20 text-green-700 dark:text-green-300 border-2 border-green-500/50 shadow-md'
                          : 'bg-card hover:bg-gradient-to-br hover:from-primary/5 hover:to-secondary/5 hover:shadow-elegant border border-border/30'
                    }`}
                  >
                    {/* Provider Icon */}
                    <div className={`transition-all duration-300 ${
                      isSelected ? 'scale-125 drop-shadow-lg' : 'group-hover:scale-110'
                    }`}>
                      {provider.icon}
                    </div>

                    {/* Provider Name */}
                    <span className={`text-xs font-medium text-center transition-all duration-300 ${
                      isSelected ? 'text-white' : 'text-foreground group-hover:text-primary'
                    }`}>
                      {provider.name}
                    </span>

                    {/* Model Count Badge */}
                    <div className={`text-xs px-2 py-1 rounded-full transition-all duration-300 ${
                      isSelected
                        ? 'bg-white/20 text-white'
                        : isConfigured
                          ? 'bg-green-500/20 text-green-700 dark:text-green-300'
                          : 'bg-muted text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary'
                    }`}>
                      {provider.models.length} models
                    </div>

                    {/* Status Indicator */}
                    {isConfigured && (
                      <div className={`absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center shadow-lg ${
                        isSelected
                          ? 'bg-white text-primary animate-pulse'
                          : 'bg-green-500 text-white animate-bounce'
                      }`}>
                        <div className="w-3 h-3 rounded-full bg-current"></div>
                      </div>
                    )}

                    {/* Selection Glow Effect */}
                    {isSelected && (
                      <div className="absolute inset-0 rounded-2xl bg-gradient-primary opacity-20 blur-xl"></div>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>
          </div>

          {/* Provider Configuration Sections with Enhanced Spacing */}
          <div className="mt-16">
            {API_PROVIDERS.map((provider) => {
              const isCurrentProvider = currentConfig?.provider === provider.id;
              return (
                <TabsContent key={provider.id} value={provider.id} className="space-y-8 mt-8">
                <Card className={`transition-all duration-300 ${
                  isCurrentProvider
                    ? 'border-2 border-green-500 bg-gradient-to-br from-green-50/50 to-green-100/30 dark:from-green-900/20 dark:to-green-800/10 shadow-glow'
                    : 'border border-border/50 bg-gradient-card hover:shadow-elegant'
                }`}>
                  <CardHeader className="pb-6">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-3">
                        <div className={`p-3 rounded-2xl transition-all duration-300 ${
                          isCurrentProvider
                            ? 'bg-green-500 text-white shadow-glow'
                            : 'bg-gradient-primary text-white'
                        }`}>
                          {provider.icon}
                        </div>
                        <div>
                          <div className="flex items-center space-x-3">
                            <span className="text-xl font-bold">{provider.name}</span>
                            <Badge variant="secondary" className="px-3 py-1">
                              {provider.models.length} models
                            </Badge>
                          </div>
                          {isCurrentProvider && (
                            <Badge variant="default" className="bg-green-600 hover:bg-green-700 mt-2">
                              ✓ Currently Active
                            </Badge>
                          )}
                        </div>
                      </CardTitle>

                      {/* Provider Website Link */}
                      <a
                        href={provider.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-muted-foreground hover:text-primary transition-colors flex items-center space-x-1"
                      >
                        <span>Get API Key</span>
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>

                    <CardDescription className="text-base leading-relaxed">
                      Configure your {provider.name} API settings to access {provider.models.length} powerful AI models
                      {isCurrentProvider && (
                        <div className="mt-3 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg border border-green-200 dark:border-green-800">
                          <span className="text-green-700 dark:text-green-300 font-medium flex items-center space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span>This provider is currently active across all tools</span>
                          </span>
                        </div>
                      )}
                    </CardDescription>
                  </CardHeader>
                <CardContent className="space-y-4">
                  {/* Model Selection */}
                  <div className="space-y-2">
                    <Label htmlFor={`model-${provider.id}`}>Select Model</Label>
                    <Select 
                      value={selectedProvider === provider.id ? selectedModel : ''} 
                      onValueChange={setSelectedModel}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a model" />
                      </SelectTrigger>
                      <SelectContent className="max-h-[300px]">
                        {provider.models.map((model) => {
                          const isCurrentModel = currentConfig?.provider === provider.id && currentConfig?.model === model.id;
                          return (
                            <SelectItem
                              key={model.id}
                              value={model.id}
                              className={`py-3 ${isCurrentModel ? 'bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500' : ''}`}
                            >
                              <div className="flex items-center justify-between w-full">
                                <div className="flex flex-col">
                                  <div className="flex items-center space-x-2">
                                    <span className="font-medium text-sm">{model.name}</span>
                                    {isCurrentModel && (
                                      <div className="w-2 h-2 rounded-full bg-green-500" />
                                    )}
                                  </div>
                                  <span className="text-xs text-muted-foreground mt-1 leading-relaxed">{model.description}</span>
                                </div>
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* API Key Input */}
                  <div className="space-y-2">
                    <Label htmlFor={`apikey-${provider.id}`}>API Key</Label>
                    <div className="relative">
                      <Input
                        id={`apikey-${provider.id}`}
                        type={showApiKey ? "text" : "password"}
                        placeholder={provider.keyPlaceholder}
                        value={selectedProvider === provider.id ? apiKey : ''}
                        onChange={(e) => setApiKey(e.target.value)}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Get your API key from{' '}
                      <a 
                        href={provider.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {provider.name} Dashboard
                      </a>
                    </p>
                  </div>

                  {/* Model Details */}
                  {selectedModel && selectedProvider === provider.id && (
                    <div className="p-3 bg-muted/30 rounded-lg border">
                      <h4 className="font-medium mb-2">Selected Model Details</h4>
                      {(() => {
                        const model = provider.models.find(m => m.id === selectedModel);
                        return model ? (
                          <div>
                            <p className="text-sm font-medium">{model.name}</p>
                            <p className="text-xs text-muted-foreground">{model.description}</p>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          );
        })}
          </div>
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export the API providers for use in other components
export { API_PROVIDERS };
