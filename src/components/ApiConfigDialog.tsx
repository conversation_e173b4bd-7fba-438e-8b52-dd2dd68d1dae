import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Eye, EyeOff, Key, Zap, Brain, Sparkles, Bot, Cpu } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export interface ApiProvider {
  id: string;
  name: string;
  icon: React.ReactNode;
  models: { id: string; name: string; description: string }[];
  keyPlaceholder: string;
  website: string;
}

export interface ApiConfig {
  provider: string;
  model: string;
  apiKey: string;
}

const API_PROVIDERS: ApiProvider[] = [
  {
    id: 'openai',
    name: 'OpenAI',
    icon: <Brain className="h-5 w-5" />,
    models: [
      { id: 'gpt-4o', name: 'GPT-4o', description: 'Most capable model, multimodal, 128K context' },
      { id: 'gpt-4o-2024-11-20', name: 'GPT-4o (Nov 2024)', description: 'Latest GPT-4o with improved capabilities' },
      { id: 'gpt-4o-2024-08-06', name: 'GPT-4o (Aug 2024)', description: 'Structured outputs, function calling' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini', description: 'Faster, cheaper, 128K context' },
      { id: 'gpt-4o-mini-2024-07-18', name: 'GPT-4o Mini (July 2024)', description: 'Cost-effective with high intelligence' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo', description: 'High performance, 128K context, vision' },
      { id: 'gpt-4-turbo-2024-04-09', name: 'GPT-4 Turbo (April 2024)', description: 'Latest GPT-4 Turbo with vision' },
      { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo Preview', description: 'Preview version with latest features' },
      { id: 'gpt-4', name: 'GPT-4', description: 'Original GPT-4, 8K context' },
      { id: 'gpt-4-0613', name: 'GPT-4 (June 2023)', description: 'Function calling, improved instruction following' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast, cost-effective, 16K context' },
      { id: 'gpt-3.5-turbo-0125', name: 'GPT-3.5 Turbo (Jan 2024)', description: 'Latest 3.5 with reduced costs' },
      { id: 'gpt-3.5-turbo-1106', name: 'GPT-3.5 Turbo (Nov 2023)', description: 'Improved instruction following' }
    ],
    keyPlaceholder: 'sk-proj-...',
    website: 'https://platform.openai.com/api-keys'
  },
  {
    id: 'anthropic',
    name: 'Claude (Anthropic)',
    icon: <Sparkles className="h-5 w-5" />,
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet (Oct 2024)', description: 'Most intelligent, computer use, 200K context' },
      { id: 'claude-3-5-sonnet-20240620', name: 'Claude 3.5 Sonnet (June 2024)', description: 'Balanced intelligence and speed' },
      { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku (Oct 2024)', description: 'Fastest model, great for simple tasks' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus', description: 'Most powerful, best for complex tasks' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet', description: 'Balanced performance and cost' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku', description: 'Fastest and most compact model' },
      { id: 'claude-2.1', name: 'Claude 2.1', description: 'Previous generation, 200K context' },
      { id: 'claude-2.0', name: 'Claude 2.0', description: 'Earlier version, good for general tasks' },
      { id: 'claude-instant-1.2', name: 'Claude Instant 1.2', description: 'Fast and affordable option' }
    ],
    keyPlaceholder: 'sk-ant-...',
    website: 'https://console.anthropic.com/settings/keys'
  },
  {
    id: 'google',
    name: 'Google Gemini',
    icon: <Zap className="h-5 w-5" />,
    models: [
      { id: 'gemini-1.5-pro-latest', name: 'Gemini 1.5 Pro (Latest)', description: 'Most capable, 2M context, multimodal' },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'High-performance model with large context' },
      { id: 'gemini-1.5-pro-exp-0827', name: 'Gemini 1.5 Pro Experimental', description: 'Experimental version with new features' },
      { id: 'gemini-1.5-flash-latest', name: 'Gemini 1.5 Flash (Latest)', description: 'Fast, efficient, 1M context' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash', description: 'Optimized for speed and efficiency' },
      { id: 'gemini-1.5-flash-8b-latest', name: 'Gemini 1.5 Flash 8B', description: 'Smaller, faster variant' },
      { id: 'gemini-1.0-pro-latest', name: 'Gemini 1.0 Pro (Latest)', description: 'Stable version for production' },
      { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro', description: 'Original Pro model' },
      { id: 'gemini-pro', name: 'Gemini Pro', description: 'Legacy naming for Gemini 1.0 Pro' },
      { id: 'gemini-pro-vision', name: 'Gemini Pro Vision', description: 'Multimodal with vision capabilities' }
    ],
    keyPlaceholder: 'AIza...',
    website: 'https://aistudio.google.com/app/apikey'
  },
  {
    id: 'xai',
    name: 'Grok (xAI)',
    icon: <Bot className="h-5 w-5" />,
    models: [
      { id: 'grok-beta', name: 'Grok Beta', description: 'Latest Grok with real-time X data access' },
      { id: 'grok-vision-beta', name: 'Grok Vision Beta', description: 'Multimodal Grok with image understanding' },
      { id: 'grok-2-latest', name: 'Grok-2 (Latest)', description: 'Most advanced Grok model' },
      { id: 'grok-2-1212', name: 'Grok-2 (Dec 2024)', description: 'Enhanced reasoning and knowledge' },
      { id: 'grok-2-vision-1212', name: 'Grok-2 Vision (Dec 2024)', description: 'Advanced vision capabilities' },
      { id: 'grok-2-mini', name: 'Grok-2 Mini', description: 'Smaller, faster version of Grok-2' }
    ],
    keyPlaceholder: 'xai-...',
    website: 'https://console.x.ai/team/api-keys'
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    icon: <Cpu className="h-5 w-5" />,
    models: [
      { id: 'deepseek-chat', name: 'DeepSeek Chat', description: 'General purpose conversational AI' },
      { id: 'deepseek-coder', name: 'DeepSeek Coder', description: 'Specialized for programming tasks' },
      { id: 'deepseek-v2.5', name: 'DeepSeek V2.5', description: 'Latest version with improved capabilities' },
      { id: 'deepseek-v2', name: 'DeepSeek V2', description: 'Advanced reasoning and knowledge' },
      { id: 'deepseek-coder-v2', name: 'DeepSeek Coder V2', description: 'Enhanced coding capabilities' },
      { id: 'deepseek-math', name: 'DeepSeek Math', description: 'Specialized for mathematical reasoning' },
      { id: 'deepseek-reasoning', name: 'DeepSeek Reasoning', description: 'Optimized for logical reasoning tasks' }
    ],
    keyPlaceholder: 'sk-...',
    website: 'https://platform.deepseek.com/api_keys'
  }
];

interface ApiConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfigSaved: (config: ApiConfig) => void;
  currentConfig?: ApiConfig;
}

export function ApiConfigDialog({ open, onOpenChange, onConfigSaved, currentConfig }: ApiConfigDialogProps) {
  const [selectedProvider, setSelectedProvider] = useState(currentConfig?.provider || 'openai');
  const [selectedModel, setSelectedModel] = useState(currentConfig?.model || '');
  const [apiKey, setApiKey] = useState(currentConfig?.apiKey || '');
  const [showApiKey, setShowApiKey] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (currentConfig) {
      setSelectedProvider(currentConfig.provider);
      setSelectedModel(currentConfig.model);
      setApiKey(currentConfig.apiKey);
    }
  }, [currentConfig]);

  const handleProviderChange = (providerId: string) => {
    setSelectedProvider(providerId);
    setSelectedModel(''); // Reset model when provider changes
    setApiKey(''); // Reset API key when provider changes
  };

  const handleSave = () => {
    if (!selectedProvider || !selectedModel || !apiKey) {
      toast({
        title: "Missing Information",
        description: "Please select a provider, model, and enter your API key.",
        variant: "destructive"
      });
      return;
    }

    const config: ApiConfig = {
      provider: selectedProvider,
      model: selectedModel,
      apiKey: apiKey.trim()
    };

    onConfigSaved(config);
    onOpenChange(false);
    
    toast({
      title: "API Configuration Saved",
      description: `Now using ${API_PROVIDERS.find(p => p.id === selectedProvider)?.name} with ${API_PROVIDERS.find(p => p.id === selectedProvider)?.models.find(m => m.id === selectedModel)?.name}`,
    });
  };

  const selectedProviderData = API_PROVIDERS.find(p => p.id === selectedProvider);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5" />
            <span>API Configuration</span>
          </DialogTitle>
          <DialogDescription>
            Choose your preferred AI provider and configure your API settings
          </DialogDescription>
        </DialogHeader>

        <Tabs value={selectedProvider} onValueChange={handleProviderChange} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            {API_PROVIDERS.map((provider) => (
              <TabsTrigger key={provider.id} value={provider.id} className="flex items-center space-x-2">
                {provider.icon}
                <span className="hidden sm:inline">{provider.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {API_PROVIDERS.map((provider) => (
            <TabsContent key={provider.id} value={provider.id} className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    {provider.icon}
                    <span>{provider.name}</span>
                    <Badge variant="secondary">
                      {provider.models.length} models
                    </Badge>
                  </CardTitle>
                  <CardDescription>
                    Configure your {provider.name} API settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Model Selection */}
                  <div className="space-y-2">
                    <Label htmlFor={`model-${provider.id}`}>Select Model</Label>
                    <Select 
                      value={selectedProvider === provider.id ? selectedModel : ''} 
                      onValueChange={setSelectedModel}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a model" />
                      </SelectTrigger>
                      <SelectContent>
                        {provider.models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex flex-col">
                              <span className="font-medium">{model.name}</span>
                              <span className="text-xs text-muted-foreground">{model.description}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* API Key Input */}
                  <div className="space-y-2">
                    <Label htmlFor={`apikey-${provider.id}`}>API Key</Label>
                    <div className="relative">
                      <Input
                        id={`apikey-${provider.id}`}
                        type={showApiKey ? "text" : "password"}
                        placeholder={provider.keyPlaceholder}
                        value={selectedProvider === provider.id ? apiKey : ''}
                        onChange={(e) => setApiKey(e.target.value)}
                        className="pr-10"
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Get your API key from{' '}
                      <a 
                        href={provider.website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {provider.name} Dashboard
                      </a>
                    </p>
                  </div>

                  {/* Model Details */}
                  {selectedModel && selectedProvider === provider.id && (
                    <div className="p-3 bg-muted/30 rounded-lg border">
                      <h4 className="font-medium mb-2">Selected Model Details</h4>
                      {(() => {
                        const model = provider.models.find(m => m.id === selectedModel);
                        return model ? (
                          <div>
                            <p className="text-sm font-medium">{model.name}</p>
                            <p className="text-xs text-muted-foreground">{model.description}</p>
                          </div>
                        ) : null;
                      })()}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Export the API providers for use in other components
export { API_PROVIDERS };
