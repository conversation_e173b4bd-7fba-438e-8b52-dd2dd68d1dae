import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ApiConfig } from "./ApiConfigDialog";
import { createAIService } from "@/services/aiService";
import { FileText, Sparkles, Target, Users, Search, Copy, Download } from "lucide-react";

interface SEOContentGeneratorProps {
  apiConfig?: ApiConfig | null;
  onApiKeyRequest?: () => void;
}

interface GeneratedContent {
  title: string;
  metaTitle: string;
  metaDescription: string;
  urlSlug: string;
  metaTags: string[];
  content: string;
  wordCount: number;
  keywordDensity: string;
  readabilityScore: string;
  seoScore: string;
  improvements: string[];
}

export function SEOContentGenerator({ apiConfig, onApiKeyRequest }: SEOContentGeneratorProps) {
  const [topic, setTopic] = useState("");
  const [targetWordCount, setTargetWordCount] = useState("1500");
  const [primaryKeyword, setPrimaryKeyword] = useState("");
  const [secondaryKeywords, setSecondaryKeywords] = useState("");
  const [targetAudience, setTargetAudience] = useState("");
  const [contentType, setContentType] = useState("how-to");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);

  const contentTypes = [
    { value: "how-to", label: "How-to Guide" },
    { value: "listicle", label: "List Article" },
    { value: "comparison", label: "Comparison/Review" },
    { value: "ultimate-guide", label: "Ultimate Guide" },
    { value: "case-study", label: "Case Study" },
    { value: "news-analysis", label: "News & Analysis" },
    { value: "tutorial", label: "Tutorial" },
    { value: "resource-roundup", label: "Resource Roundup" }
  ];

  const handleGenerate = async () => {
    if (!apiConfig) {
      toast({
        title: "API Configuration Required",
        description: "Please configure your AI API settings first.",
        variant: "destructive",
      });
      if (onApiKeyRequest) {
        onApiKeyRequest();
      }
      return;
    }

    if (!topic.trim() || !primaryKeyword.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both a topic and primary keyword.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const aiService = createAIService(apiConfig);
      
      toast({
        title: "Content Generation Started",
        description: `Creating SEO-optimized content with ${aiService.getProviderName()}...`,
      });

      const prompt = `You are both an elite-level SEO strategist and a trusted subject-matter expert writer. Your goal: craft a long-form, **people-first**, SEO-optimized article in fluent professional English that ranks on Google's first page **and genuinely helps readers**. Use AI responsibly as a tool—not a shortcut—and follow Google's guidelines for E‑E‑A‑T and AI-generated content.

Write a comprehensive article on "${topic}" targeting ${targetWordCount} words for ${targetAudience || "general readers interested in the subject"}.

Primary Keyword: "${primaryKeyword}"
Secondary Keywords: "${secondaryKeywords}"
Content Type: "${contentType}"

### 1. Who, How & Why (E‑E‑A‑T + AI Transparency)
- **Who**: Include an author byline and credentials—mention real-world experience or expertise to establish authority.
- **How**: Provide a brief "How this was created" note, including use of AI tools (e.g., for research, outline, draft), with clear human editorial oversight.
- **Why**: Clearly state that the article aims to help readers solve real problems—not to manipulate SEO or ranking systems.

**Structural & Readability Guidelines**
1. Craft a compelling, keyword-rich title (include primary keyword).
2. Write an engaging intro that hooks the audience, defines the topic, and previews main takeaways include primary keyword in the first paragraph.
3. Organize 3–5 body sections under clear subheadings—one must include the primary keyword.
4. Use natural keyword placement (1–1.5% density); include primary, secondary, long-tail keywords, and semantic variants.
5. Integrate real-world examples, original insights, expert quotes, data, or case studies to deepen value.
6. Add practical takeaways, actionable advice, bullet points, numbered lists, visuals.
7. Ensure the tone is conversational yet authoritative, at an 8th–9th grade reading level.
8. Maintain short paragraphs (2–3 sentences), avoid jargon, and use semantic headings.
9. Include [LINK] placeholders for internal linking.
10. **Avoid** overused AI-style openers (e.g., "In today's digital world…").

**Page Experience & People‑First Quality**
- Ensure clear descriptive headings, fast-loading structure, mobile-friendly layout.
- Use subheadings, bullet lists, tables, and images to enhance readability.
- Encourage sharing, bookmarking, and reader feedback.
- Include an author bio linking to credentials or an About page.

**SEO Requirements**
- Use the primary keyword in the title, first paragraph, at least one subheading, alt text, and slug.
- Maintain 1–1.5% keyword density (max 2.5%).
- Sprinkle in related long-tail keywords and semantic terms.
- Prepare metadata:  
  • Meta title (50–60 characters, include keyword)  
  • Meta description (150–160 chars, include keyword)  
  • Image alt-text (50–60 chars, include keyword)  
  • URL slug  
  • Meta Tags Contain the main keywords, related and the long tail keywords

**Content Outline**
- **Introduction**: Why the topic matters + what readers'll learn.
- **Section 1**: Deep dive into the first subtopic—research, original analysis, examples.
- **Section 2**: Further exploration/insights/data/quotes.
- **Section 3**: Practical or advanced applications.
- Add optional Sections 4–5 for FAQs, tips, tools, or case studies.
- **Conclusion** optional and Not necessary if the topic is not worth it: Summarize, highlight key takeaways, include a clear call-to-action, encourage engagement.

###Responsible Use of AI Content
- Use AI to assist with research, outlining, drafting—not to mass-produce.
- Always **fact-check**, **edit for clarity**, and **add human insight**.
- Disclose AI use in a "How this was created" note.
- Do **not** claim AI as the author; AI is a tool, not the author.

**Image Placement**
- [INSERT_IMAGE_1_DESCRIPTION] – after introduction, visually illustrating a core concept.
- [INSERT_IMAGE_2_DESCRIPTION] – mid-article, to break text and clarify complex insights.

**Audit & Quality Checklist**
After writing, score your own piece:
- ✅ Original analysis and added value?  
- ✅ Comprehensive topic coverage?  
- ✅ Clear byline and author credentials?  
- ✅ AI disclosure and transparent method  
- ✅ Transparent "How" explanation?  
- ✅ People-first tone, not keyword-stuffed?  
- ✅ Reader-friendly page experience?  
- ✅ Proper metadata & keyword usage?  
- ✅ Actionable advice given?  
- ✅ Internal links added?  
- ✅ Encouraged feedback/sharing?

Include your self‑audit at the end with a score out of 10 and notes on any improvements planned.

Please format your response as JSON with this structure:
{
  "title": "Article title",
  "metaTitle": "SEO meta title (50-60 chars)",
  "metaDescription": "SEO meta description (150-160 chars)",
  "urlSlug": "seo-friendly-url-slug",
  "metaTags": ["keyword1", "keyword2", "keyword3"],
  "content": "Full article content with HTML formatting",
  "wordCount": 1500,
  "keywordDensity": "1.2%",
  "readabilityScore": "8th grade",
  "seoScore": "9/10",
  "improvements": ["Suggestion 1", "Suggestion 2"]
}`;

      const response = await aiService.generateContent(prompt);
      
      // Clean up the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const contentResult = JSON.parse(jsonMatch[0]);
        setGeneratedContent(contentResult);
        
        toast({
          title: "Content Generated Successfully!",
          description: `Created ${contentResult.wordCount} words of SEO-optimized content using ${aiService.getProviderName()}.`,
        });
      } else {
        throw new Error("Invalid response format from AI service");
      }
    } catch (error) {
      console.error('Content generation error:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "There was an error generating content. Please check your API configuration and try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to Clipboard",
      description: "Content has been copied to your clipboard.",
    });
  };

  const downloadContent = () => {
    if (!generatedContent) return;
    
    const content = `# ${generatedContent.title}

## SEO Metadata
- **Meta Title**: ${generatedContent.metaTitle}
- **Meta Description**: ${generatedContent.metaDescription}
- **URL Slug**: ${generatedContent.urlSlug}
- **Meta Tags**: ${generatedContent.metaTags.join(', ')}

## Content Statistics
- **Word Count**: ${generatedContent.wordCount}
- **Keyword Density**: ${generatedContent.keywordDensity}
- **Readability**: ${generatedContent.readabilityScore}
- **SEO Score**: ${generatedContent.seoScore}

## Article Content

${generatedContent.content}

## Improvement Suggestions
${generatedContent.improvements.map(imp => `- ${imp}`).join('\n')}
`;

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${generatedContent.urlSlug}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Content Downloaded",
      description: "Your SEO content has been saved as a Markdown file.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <FileText className="h-8 w-8 text-blue-600" />
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            SEO Content Generator
          </h2>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Generate comprehensive, SEO-optimized articles that rank on Google's first page while genuinely helping readers. 
          Built with E-E-A-T guidelines and responsible AI practices.
        </p>
      </div>

      {/* Input Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Content Requirements</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="topic">Article Topic *</Label>
              <Input
                id="topic"
                placeholder="e.g., How to Start a Successful Blog in 2024"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="primaryKeyword">Primary Keyword *</Label>
              <Input
                id="primaryKeyword"
                placeholder="e.g., start a blog"
                value={primaryKeyword}
                onChange={(e) => setPrimaryKeyword(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="secondaryKeywords">Secondary Keywords (comma-separated)</Label>
            <Input
              id="secondaryKeywords"
              placeholder="e.g., blogging tips, blog monetization, content creation"
              value={secondaryKeywords}
              onChange={(e) => setSecondaryKeywords(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetWordCount">Target Word Count</Label>
              <Select value={targetWordCount} onValueChange={setTargetWordCount}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="800">800 words</SelectItem>
                  <SelectItem value="1200">1,200 words</SelectItem>
                  <SelectItem value="1500">1,500 words</SelectItem>
                  <SelectItem value="2000">2,000 words</SelectItem>
                  <SelectItem value="2500">2,500 words</SelectItem>
                  <SelectItem value="3000">3,000 words</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="contentType">Content Type</Label>
              <Select value={contentType} onValueChange={setContentType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {contentTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetAudience">Target Audience</Label>
              <Input
                id="targetAudience"
                placeholder="e.g., beginner bloggers"
                value={targetAudience}
                onChange={(e) => setTargetAudience(e.target.value)}
              />
            </div>
          </div>

          <Button 
            onClick={handleGenerate} 
            disabled={isGenerating || !apiConfig}
            className="w-full"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Sparkles className="mr-2 h-4 w-4 animate-spin" />
                Generating SEO Content...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Generate SEO-Optimized Article
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Content Display */}
      {generatedContent && (
        <div className="space-y-6">
          {/* SEO Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>SEO Metadata</span>
                <div className="flex space-x-2 ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(`Title: ${generatedContent.metaTitle}\nDescription: ${generatedContent.metaDescription}\nSlug: ${generatedContent.urlSlug}\nTags: ${generatedContent.metaTags.join(', ')}`)}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Metadata
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadContent}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download All
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Meta Title</Label>
                  <p className="text-sm bg-muted p-2 rounded mt-1">{generatedContent.metaTitle}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">URL Slug</Label>
                  <p className="text-sm bg-muted p-2 rounded mt-1">{generatedContent.urlSlug}</p>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Meta Description</Label>
                <p className="text-sm bg-muted p-2 rounded mt-1">{generatedContent.metaDescription}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Meta Tags</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {generatedContent.metaTags.map((tag, index) => (
                    <Badge key={index} variant="secondary">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Content Analysis</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{generatedContent.wordCount}</div>
                  <div className="text-sm text-muted-foreground">Words</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{generatedContent.keywordDensity}</div>
                  <div className="text-sm text-muted-foreground">Keyword Density</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{generatedContent.readabilityScore}</div>
                  <div className="text-sm text-muted-foreground">Readability</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{generatedContent.seoScore}</div>
                  <div className="text-sm text-muted-foreground">SEO Score</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Article Content */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Generated Article</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(generatedContent.content)}
                >
                  <Copy className="h-4 w-4 mr-1" />
                  Copy Article
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                className="prose prose-sm max-w-none bg-muted/30 p-6 rounded-lg border"
                dangerouslySetInnerHTML={{ __html: generatedContent.content }}
              />
            </CardContent>
          </Card>

          {/* Improvement Suggestions */}
          {generatedContent.improvements.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-5 w-5" />
                  <span>Improvement Suggestions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {generatedContent.improvements.map((improvement, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">{improvement}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
