import { useState } from "react";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ApiConfig } from "./ApiConfigDialog";
import { createAIService } from "@/services/aiService";
import { FileText, Sparkles, Target, Users, Search, Copy, Download } from "lucide-react";

interface SEOContentGeneratorProps {
  apiConfig?: ApiConfig | null;
  onApiKeyRequest?: () => void;
}

interface GeneratedContent {
  title: string;
  metaTitle: string;
  metaDescription: string;
  urlSlug: string;
  metaTags: string[];
  content: string;
  wordCount: number;
  keywordDensity: string;
  readabilityScore: string;
  seoScore: string;
  improvements: string[];
}

export function SEOContentGenerator({ apiConfig, onApiKeyRequest }: SEOContentGeneratorProps) {
  const [topic, setTopic] = useState("");
  const [targetWordCount, setTargetWordCount] = useState("1500");
  const [primaryKeyword, setPrimaryKeyword] = useState("");
  const [secondaryKeywords, setSecondaryKeywords] = useState("");
  const [targetAudience, setTargetAudience] = useState("");
  const [contentType, setContentType] = useState("how-to");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent | null>(null);

  const contentTypes = [
    { value: "how-to", label: "How-to Guide" },
    { value: "listicle", label: "List Article" },
    { value: "comparison", label: "Comparison/Review" },
    { value: "ultimate-guide", label: "Ultimate Guide" },
    { value: "case-study", label: "Case Study" },
    { value: "news-analysis", label: "News & Analysis" },
    { value: "tutorial", label: "Tutorial" },
    { value: "resource-roundup", label: "Resource Roundup" }
  ];

  const handleGenerate = async () => {
    if (!apiConfig) {
      toast({
        title: "API Configuration Required",
        description: "Please configure your AI API settings first.",
        variant: "destructive",
      });
      if (onApiKeyRequest) {
        onApiKeyRequest();
      }
      return;
    }

    if (!topic.trim() || !primaryKeyword.trim()) {
      toast({
        title: "Missing Information",
        description: "Please provide both a topic and primary keyword.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const aiService = createAIService(apiConfig);
      
      toast({
        title: "Content Generation Started",
        description: `Creating SEO-optimized content with ${aiService.getProviderName()}...`,
      });

      const prompt = `You are both an elite-level SEO strategist and a trusted subject-matter expert writer. Your goal: craft a long-form, **people-first**, SEO-optimized article in fluent professional English that ranks on Google's first page **and genuinely helps readers**. Use AI responsibly as a tool—not a shortcut—and follow Google's guidelines for E‑E‑A‑T and AI-generated content.

Write a comprehensive article on "${topic}" targeting ${targetWordCount} words for ${targetAudience || "general readers interested in the subject"}.

Primary Keyword: "${primaryKeyword}"
Secondary Keywords: "${secondaryKeywords}"
Content Type: "${contentType}"

### 1. Who, How & Why (E‑E‑A‑T + AI Transparency)
- **Who**: Include an author byline and credentials—mention real-world experience or expertise to establish authority.
- **How**: Provide a brief "How this was created" note, including use of AI tools (e.g., for research, outline, draft), with clear human editorial oversight.
- **Why**: Clearly state that the article aims to help readers solve real problems—not to manipulate SEO or ranking systems.

**Structural & Readability Guidelines**
1. Craft a compelling, keyword-rich title (include primary keyword).
2. Write an engaging intro that hooks the audience, defines the topic, and previews main takeaways include primary keyword in the first paragraph.
3. Organize 3–5 body sections under clear subheadings—one must include the primary keyword.
4. Use natural keyword placement (1–1.5% density); include primary, secondary, long-tail keywords, and semantic variants.
5. Integrate real-world examples, original insights, expert quotes, data, or case studies to deepen value.
6. Add practical takeaways, actionable advice, bullet points, numbered lists, visuals.
7. Ensure the tone is conversational yet authoritative, at an 8th–9th grade reading level.
8. Maintain short paragraphs (2–3 sentences), avoid jargon, and use semantic headings.
9. Include [LINK] placeholders for internal linking.
10. **Avoid** overused AI-style openers (e.g., "In today's digital world…").

**Page Experience & People‑First Quality**
- Ensure clear descriptive headings, fast-loading structure, mobile-friendly layout.
- Use subheadings, bullet lists, tables, and images to enhance readability.
- Encourage sharing, bookmarking, and reader feedback.
- Include an author bio linking to credentials or an About page.

**SEO Requirements**
- Use the primary keyword in the title, first paragraph, at least one subheading, alt text, and slug.
- Maintain 1–1.5% keyword density (max 2.5%).
- Sprinkle in related long-tail keywords and semantic terms.
- Prepare metadata:  
  • Meta title (50–60 characters, include keyword)  
  • Meta description (150–160 chars, include keyword)  
  • Image alt-text (50–60 chars, include keyword)  
  • URL slug  
  • Meta Tags Contain the main keywords, related and the long tail keywords

**Content Outline**
- **Introduction**: Why the topic matters + what readers'll learn.
- **Section 1**: Deep dive into the first subtopic—research, original analysis, examples.
- **Section 2**: Further exploration/insights/data/quotes.
- **Section 3**: Practical or advanced applications.
- Add optional Sections 4–5 for FAQs, tips, tools, or case studies.
- **Conclusion** optional and Not necessary if the topic is not worth it: Summarize, highlight key takeaways, include a clear call-to-action, encourage engagement.

###Responsible Use of AI Content
- Use AI to assist with research, outlining, drafting—not to mass-produce.
- Always **fact-check**, **edit for clarity**, and **add human insight**.
- Disclose AI use in a "How this was created" note.
- Do **not** claim AI as the author; AI is a tool, not the author.

**Image Placement**
- [INSERT_IMAGE_1_DESCRIPTION] – after introduction, visually illustrating a core concept.
- [INSERT_IMAGE_2_DESCRIPTION] – mid-article, to break text and clarify complex insights.

**Audit & Quality Checklist**
After writing, score your own piece:
- ✅ Original analysis and added value?  
- ✅ Comprehensive topic coverage?  
- ✅ Clear byline and author credentials?  
- ✅ AI disclosure and transparent method  
- ✅ Transparent "How" explanation?  
- ✅ People-first tone, not keyword-stuffed?  
- ✅ Reader-friendly page experience?  
- ✅ Proper metadata & keyword usage?  
- ✅ Actionable advice given?  
- ✅ Internal links added?  
- ✅ Encouraged feedback/sharing?

Include your self‑audit at the end with a score out of 10 and notes on any improvements planned.

Please format your response as JSON with this structure:
{
  "title": "Article title",
  "metaTitle": "SEO meta title (50-60 chars)",
  "metaDescription": "SEO meta description (150-160 chars)",
  "urlSlug": "seo-friendly-url-slug",
  "metaTags": ["keyword1", "keyword2", "keyword3"],
  "content": "Full article content with HTML formatting",
  "wordCount": 1500,
  "keywordDensity": "1.2%",
  "readabilityScore": "8th grade",
  "seoScore": "9/10",
  "improvements": ["Suggestion 1", "Suggestion 2"]
}`;

      const response = await aiService.generateContent(prompt);
      
      // Clean up the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const contentResult = JSON.parse(jsonMatch[0]);
        setGeneratedContent(contentResult);
        
        toast({
          title: "Content Generated Successfully!",
          description: `Created ${contentResult.wordCount} words of SEO-optimized content using ${aiService.getProviderName()}.`,
        });
      } else {
        throw new Error("Invalid response format from AI service");
      }
    } catch (error) {
      console.error('Content generation error:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "There was an error generating content. Please check your API configuration and try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to Clipboard",
      description: "Content has been copied to your clipboard.",
    });
  };

  const downloadContent = () => {
    if (!generatedContent) return;
    
    const content = `# ${generatedContent.title}

## SEO Metadata
- **Meta Title**: ${generatedContent.metaTitle}
- **Meta Description**: ${generatedContent.metaDescription}
- **URL Slug**: ${generatedContent.urlSlug}
- **Meta Tags**: ${generatedContent.metaTags.join(', ')}

## Content Statistics
- **Word Count**: ${generatedContent.wordCount}
- **Keyword Density**: ${generatedContent.keywordDensity}
- **Readability**: ${generatedContent.readabilityScore}
- **SEO Score**: ${generatedContent.seoScore}

## Article Content

${generatedContent.content}

## Improvement Suggestions
${generatedContent.improvements.map(imp => `- ${imp}`).join('\n')}
`;

    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${generatedContent.urlSlug}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Content Downloaded",
      description: "Your SEO content has been saved as a Markdown file.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center space-x-2">
          <FileText className="h-8 w-8 text-blue-600" />
          <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            SEO Content Generator
          </h2>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Generate comprehensive, SEO-optimized articles that rank on Google's first page while genuinely helping readers. 
          Built with E-E-A-T guidelines and responsible AI practices.
        </p>
      </div>

      {/* Input Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Content Requirements</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="topic">Article Topic *</Label>
              <Input
                id="topic"
                placeholder="e.g., How to Start a Successful Blog in 2024"
                value={topic}
                onChange={(e) => setTopic(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="primaryKeyword">Primary Keyword *</Label>
              <Input
                id="primaryKeyword"
                placeholder="e.g., start a blog"
                value={primaryKeyword}
                onChange={(e) => setPrimaryKeyword(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="secondaryKeywords">Secondary Keywords (comma-separated)</Label>
            <Input
              id="secondaryKeywords"
              placeholder="e.g., blogging tips, blog monetization, content creation"
              value={secondaryKeywords}
              onChange={(e) => setSecondaryKeywords(e.target.value)}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetWordCount">Target Word Count</Label>
              <Select value={targetWordCount} onValueChange={setTargetWordCount}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="800">800 words</SelectItem>
                  <SelectItem value="1200">1,200 words</SelectItem>
                  <SelectItem value="1500">1,500 words</SelectItem>
                  <SelectItem value="2000">2,000 words</SelectItem>
                  <SelectItem value="2500">2,500 words</SelectItem>
                  <SelectItem value="3000">3,000 words</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="contentType">Content Type</Label>
              <Select value={contentType} onValueChange={setContentType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {contentTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="targetAudience">Target Audience</Label>
              <Input
                id="targetAudience"
                placeholder="e.g., beginner bloggers"
                value={targetAudience}
                onChange={(e) => setTargetAudience(e.target.value)}
              />
            </div>
          </div>

          <Button 
            onClick={handleGenerate} 
            disabled={isGenerating || !apiConfig}
            className="w-full"
            size="lg"
          >
            {isGenerating ? (
              <>
                <Sparkles className="mr-2 h-4 w-4 animate-spin" />
                Generating SEO Content...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Generate SEO-Optimized Article
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Generated Content Display */}
      {generatedContent && (
        <div className="space-y-6">
          {/* SEO Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>SEO Metadata</span>
                <div className="flex space-x-2 ml-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(`Title: ${generatedContent.metaTitle}\nDescription: ${generatedContent.metaDescription}\nSlug: ${generatedContent.urlSlug}\nTags: ${generatedContent.metaTags.join(', ')}`)}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy Metadata
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadContent}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download All
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Meta Title</Label>
                  <p className="text-sm bg-muted p-2 rounded mt-1">{generatedContent.metaTitle}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium">URL Slug</Label>
                  <p className="text-sm bg-muted p-2 rounded mt-1">{generatedContent.urlSlug}</p>
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Meta Description</Label>
                <p className="text-sm bg-muted p-2 rounded mt-1">{generatedContent.metaDescription}</p>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Meta Tags</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {generatedContent.metaTags.map((tag, index) => (
                    <Badge key={index} variant="secondary">{tag}</Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Content Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Content Analysis</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{generatedContent.wordCount}</div>
                  <div className="text-sm text-muted-foreground">Words</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{generatedContent.keywordDensity}</div>
                  <div className="text-sm text-muted-foreground">Keyword Density</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{generatedContent.readabilityScore}</div>
                  <div className="text-sm text-muted-foreground">Readability</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{generatedContent.seoScore}</div>
                  <div className="text-sm text-muted-foreground">SEO Score</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Article Content with HTML Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>Generated Article - HTML Preview</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(generatedContent.content)}
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copy HTML
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${generatedContent.metaTitle}</title>
    <meta name="description" content="${generatedContent.metaDescription}">
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        .article-header { margin-bottom: 2rem; }
        .article-title { font-size: 2.5rem; font-weight: bold; margin-bottom: 1rem; color: #1a202c; }
        .article-meta { color: #718096; font-size: 0.9rem; margin-bottom: 1rem; }
        .article-content h1 { font-size: 2rem; margin: 2rem 0 1rem 0; color: #2d3748; }
        .article-content h2 { font-size: 1.5rem; margin: 1.5rem 0 1rem 0; color: #2d3748; }
        .article-content h3 { font-size: 1.25rem; margin: 1.25rem 0 0.75rem 0; color: #2d3748; }
        .article-content p { margin-bottom: 1rem; }
        .article-content ul, .article-content ol { margin: 1rem 0; padding-left: 2rem; }
        .article-content li { margin-bottom: 0.5rem; }
        .article-content blockquote { border-left: 4px solid #4299e1; padding-left: 1rem; margin: 1.5rem 0; font-style: italic; }
        .article-content code { background: #f7fafc; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-family: monospace; }
        .article-content pre { background: #f7fafc; padding: 1rem; border-radius: 0.5rem; overflow-x: auto; }
        .article-footer { margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e2e8f0; }
    </style>
</head>
<body>
    <article>
        <header class="article-header">
            <h1 class="article-title">${generatedContent.title}</h1>
            <div class="article-meta">
                <span>Published: ${new Date().toLocaleDateString()}</span> |
                <span>Word Count: ${generatedContent.wordCount}</span> |
                <span>Reading Time: ${Math.ceil(generatedContent.wordCount / 200)} min</span>
            </div>
        </header>
        <div class="article-content">
            ${generatedContent.content}
        </div>
        <footer class="article-footer">
            <p><em>This article was created with AI assistance and human editorial oversight to ensure quality and accuracy.</em></p>
        </footer>
    </article>
</body>
</html>`;
                      const blob = new Blob([htmlContent], { type: 'text/html' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `${generatedContent.urlSlug}.html`;
                      document.body.appendChild(a);
                      a.click();
                      document.body.removeChild(a);
                      URL.revokeObjectURL(url);

                      toast({
                        title: "HTML File Downloaded",
                        description: "Complete HTML file with styling ready for your website.",
                      });
                    }}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Download HTML
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                  <p className="font-medium text-blue-800 dark:text-blue-200 mb-1">📝 HTML Preview</p>
                  <p>This preview shows how your article will appear on your website with proper HTML formatting, semantic structure, and styling.</p>
                </div>

                {/* Enhanced HTML Article View */}
                <div className="bg-gradient-to-br from-white via-gray-50/50 to-white dark:from-gray-900 dark:via-gray-800/50 dark:to-gray-900 rounded-2xl border-2 border-gray-200/50 dark:border-gray-700/50 shadow-xl overflow-hidden">
                  {/* Browser-like Header */}
                  <div className="bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 px-6 py-4 border-b border-gray-300 dark:border-gray-600">
                    <div className="flex items-center space-x-3">
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      </div>
                      <div className="flex-1 bg-white dark:bg-gray-900 rounded-lg px-4 py-2 text-sm text-gray-600 dark:text-gray-400 font-mono">
                        https://yourwebsite.com/{generatedContent.urlSlug}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">HTML Preview</div>
                    </div>
                  </div>

                  {/* Article Content */}
                  <div className="p-12 bg-white dark:bg-gray-900">
                    <article className="max-w-4xl mx-auto">
                      {/* SEO-Optimized Article Header */}
                      <header className="mb-12 text-center">
                        <div className="mb-6">
                          <span className="inline-block px-4 py-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded-full text-sm font-medium">
                            {generatedContent.contentType || 'SEO Article'}
                          </span>
                        </div>
                        <h1 className="text-5xl font-bold text-gray-900 dark:text-gray-100 mb-6 leading-tight tracking-tight">
                          {generatedContent.title}
                        </h1>
                        <div className="flex items-center justify-center space-x-6 text-gray-600 dark:text-gray-400 mb-8">
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span className="text-sm">Published {new Date().toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span className="text-sm">{generatedContent.wordCount} words</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                            <span className="text-sm">{Math.ceil(generatedContent.wordCount / 200)} min read</span>
                          </div>
                        </div>
                        <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
                      </header>

                      {/* Enhanced Article Content with Professional Styling */}
                      <div
                        className="prose prose-xl dark:prose-invert max-w-none
                          prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-headings:font-bold prose-headings:tracking-tight
                          prose-h1:text-4xl prose-h1:mb-8 prose-h1:mt-12 prose-h1:pb-4 prose-h1:border-b prose-h1:border-gray-200 dark:prose-h1:border-gray-700
                          prose-h2:text-3xl prose-h2:mb-6 prose-h2:mt-10 prose-h2:text-blue-900 dark:prose-h2:text-blue-100
                          prose-h3:text-2xl prose-h3:mb-4 prose-h3:mt-8 prose-h3:text-gray-800 dark:prose-h3:text-gray-200
                          prose-h4:text-xl prose-h4:mb-3 prose-h4:mt-6 prose-h4:text-gray-700 dark:prose-h4:text-gray-300
                          prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-p:mb-6 prose-p:leading-relaxed prose-p:text-lg
                          prose-ul:my-8 prose-ol:my-8 prose-ul:space-y-3 prose-ol:space-y-3
                          prose-li:text-gray-700 dark:prose-li:text-gray-300 prose-li:leading-relaxed prose-li:text-lg
                          prose-strong:text-gray-900 dark:prose-strong:text-gray-100 prose-strong:font-bold
                          prose-em:text-gray-800 dark:prose-em:text-gray-200 prose-em:italic
                          prose-blockquote:border-l-4 prose-blockquote:border-blue-500 prose-blockquote:pl-8 prose-blockquote:py-4 prose-blockquote:italic prose-blockquote:text-gray-600 dark:prose-blockquote:text-gray-400 prose-blockquote:bg-blue-50/50 dark:prose-blockquote:bg-blue-900/20 prose-blockquote:rounded-r-lg
                          prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-3 prose-code:py-1 prose-code:rounded-md prose-code:text-sm prose-code:font-mono prose-code:text-blue-600 dark:prose-code:text-blue-400
                          prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:p-6 prose-pre:rounded-xl prose-pre:overflow-x-auto prose-pre:border prose-pre:border-gray-200 dark:prose-pre:border-gray-700
                          prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline prose-a:font-medium prose-a:transition-colors
                          prose-img:rounded-xl prose-img:shadow-lg prose-img:border prose-img:border-gray-200 dark:prose-img:border-gray-700
                          prose-hr:border-gray-300 dark:prose-hr:border-gray-600 prose-hr:my-12
                          prose-table:border-collapse prose-table:border prose-table:border-gray-300 dark:prose-table:border-gray-600
                          prose-th:bg-gray-50 dark:prose-th:bg-gray-800 prose-th:p-4 prose-th:text-left prose-th:font-semibold
                          prose-td:p-4 prose-td:border-t prose-td:border-gray-200 dark:prose-td:border-gray-700"
                        dangerouslySetInnerHTML={{ __html: generatedContent.content }}
                      />

                      {/* Professional Article Footer */}
                      <footer className="mt-16 pt-8 border-t-2 border-gray-200 dark:border-gray-700">
                        <div className="bg-gradient-to-r from-blue-50 via-purple-50 to-blue-50 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-blue-900/20 p-8 rounded-2xl border border-blue-200 dark:border-blue-800">
                          <div className="flex items-start space-x-4">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                              <span className="text-white text-xl">🤖</span>
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">AI Transparency Notice</h4>
                              <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                                This article was created with AI assistance and human editorial oversight to ensure quality, accuracy, and adherence to Google's E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness) guidelines. The content has been optimized for search engines while maintaining readability and value for human readers.
                              </p>
                            </div>
                          </div>
                        </div>
                      </footer>
                    </article>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Enhanced Improvement Suggestions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Sparkles className="h-5 w-5" />
                <span>Content Enhancement Suggestions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Always include these specific suggestions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span className="font-medium text-blue-800 dark:text-blue-200">Add Real-World Examples</span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Include specific case studies, statistics, or practical examples to make your content more actionable and credible.
                    </p>
                  </div>

                  <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="font-medium text-green-800 dark:text-green-200">Include Expert Quotes</span>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Add quotes from industry experts, thought leaders, or authoritative sources to enhance E-E-A-T and build trust.
                    </p>
                  </div>
                </div>

                {/* AI-generated suggestions if available */}
                {generatedContent.improvements.length > 0 && (
                  <div>
                    <h4 className="font-medium text-foreground mb-3">Additional AI Suggestions:</h4>
                    <ul className="space-y-3">
                      {generatedContent.improvements.map((improvement, index) => (
                        <li key={index} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                          <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-sm text-foreground">{improvement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Additional Enhancement Tips */}
                <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                  <h4 className="font-medium text-purple-800 dark:text-purple-200 mb-3">💡 Pro Enhancement Tips:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div className="flex items-start space-x-2">
                      <span className="text-purple-600 dark:text-purple-400">📊</span>
                      <span className="text-purple-700 dark:text-purple-300">Add data visualizations or infographics</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-purple-600 dark:text-purple-400">🔗</span>
                      <span className="text-purple-700 dark:text-purple-300">Include internal links to related content</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-purple-600 dark:text-purple-400">📱</span>
                      <span className="text-purple-700 dark:text-purple-300">Optimize for mobile readability</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-purple-600 dark:text-purple-400">🎯</span>
                      <span className="text-purple-700 dark:text-purple-300">Add clear call-to-action buttons</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
