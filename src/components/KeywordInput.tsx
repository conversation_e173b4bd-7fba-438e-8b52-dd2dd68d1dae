
import { useState } from "react";
import { Upload, Type, FileText, Plus, Trash2, <PERSON>rk<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export interface Keyword {
  keyword: string;
  kd?: number;
  volume?: number;
  intent?: string;
}

interface KeywordInputProps {
  keywords: Keyword[];
  onKeywordsChange: (keywords: Keyword[]) => void;
  onAnalyze: () => void;
  isLoading: boolean;
}

export function KeywordInput({ keywords, onKeywordsChange, onAnalyze, isLoading }: KeywordInputProps) {
  const [manualInput, setManualInput] = useState("");
  const [csvInput, setCsvInput] = useState("");

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      if (file.name.endsWith('.csv')) {
        setCsvInput(text);
        parseCsvData(text);
      } else {
        setManualInput(text);
        parseTextData(text);
      }
    };
    reader.readAsText(file);
  };

  const parseCsvData = (csvText: string) => {
    const lines = csvText.split('\n').filter(line => line.trim());
    const headers = lines[0].toLowerCase().split(',').map(h => h.trim());
    
    const keywordIndex = headers.findIndex(h => h.includes('keyword') || h.includes('query'));
    const kdIndex = headers.findIndex(h => h.includes('kd') || h.includes('difficulty'));
    const volumeIndex = headers.findIndex(h => h.includes('volume') || h.includes('search'));
    const intentIndex = headers.findIndex(h => h.includes('intent') || h.includes('type'));

    const newKeywords: Keyword[] = lines.slice(1).map(line => {
      const columns = line.split(',').map(c => c.trim().replace(/"/g, ''));
      return {
        keyword: columns[keywordIndex] || columns[0] || '',
        kd: kdIndex >= 0 ? parseInt(columns[kdIndex]) : undefined,
        volume: volumeIndex >= 0 ? parseInt(columns[volumeIndex]) : undefined,
        intent: intentIndex >= 0 ? columns[intentIndex] : undefined,
      };
    }).filter(k => k.keyword);

    onKeywordsChange([...keywords, ...newKeywords]);
  };

  const parseTextData = (text: string) => {
    const lines = text.split('\n').filter(line => line.trim());
    const newKeywords: Keyword[] = lines.map(line => ({
      keyword: line.trim(),
    }));
    onKeywordsChange([...keywords, ...newKeywords]);
  };

  const handleManualAdd = () => {
    if (!manualInput.trim()) return;
    parseTextData(manualInput);
    setManualInput("");
  };

  const removeKeyword = (index: number) => {
    const newKeywords = keywords.filter((_, i) => i !== index);
    onKeywordsChange(newKeywords);
  };

  const clearAll = () => {
    onKeywordsChange([]);
    setManualInput("");
    setCsvInput("");
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-card border-border shadow-soft overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5 border-b border-border/50">
          <CardTitle className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-primary rounded-lg shadow-sm">
              <Upload className="h-5 w-5 text-primary-foreground" />
            </div>
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Import Keywords & Questions
            </span>
          </CardTitle>
          <CardDescription className="text-muted-foreground/80">
            Upload your keyword list or enter them manually. Include KD and volume data for better analysis.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <Tabs defaultValue="manual" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3 p-1 bg-muted/50 rounded-xl h-auto">
              <TabsTrigger 
                value="manual" 
                className="flex items-center space-x-2 py-3 px-4 rounded-lg data-[state=active]:bg-gradient-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md transition-all duration-200 hover:bg-muted/80"
              >
                <div className="p-1.5 bg-current/20 rounded-md">
                  <Type className="h-4 w-4" />
                </div>
                <span className="font-medium">Manual Entry</span>
              </TabsTrigger>
              <TabsTrigger 
                value="file" 
                className="flex items-center space-x-2 py-3 px-4 rounded-lg data-[state=active]:bg-gradient-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md transition-all duration-200 hover:bg-muted/80"
              >
                <div className="p-1.5 bg-current/20 rounded-md">
                  <FileText className="h-4 w-4" />
                </div>
                <span className="font-medium">File Upload</span>
              </TabsTrigger>
              <TabsTrigger 
                value="csv" 
                className="flex items-center space-x-2 py-3 px-4 rounded-lg data-[state=active]:bg-gradient-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md transition-all duration-200 hover:bg-muted/80"
              >
                <div className="p-1.5 bg-current/20 rounded-md">
                  <Upload className="h-4 w-4" />
                </div>
                <span className="font-medium">CSV Data</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="manual" className="space-y-4 mt-6">
              <div className="space-y-3">
                <Label htmlFor="manual-keywords" className="text-sm font-medium text-foreground">
                  Enter keywords (one per line)
                </Label>
                <div className="relative">
                  <Textarea
                    id="manual-keywords"
                    placeholder="best coffee shops in seattle&#10;how to make perfect espresso&#10;coffee bean types comparison&#10;latte art techniques"
                    value={manualInput}
                    onChange={(e) => setManualInput(e.target.value)}
                    className="min-h-32 resize-none border-border/60 focus:border-primary/60 bg-background/50 backdrop-blur-sm"
                  />
                  <div className="absolute top-3 right-3 text-xs text-muted-foreground bg-muted/80 px-2 py-1 rounded">
                    {manualInput.split('\n').filter(line => line.trim()).length} keywords
                  </div>
                </div>
              </div>
              <Button 
                onClick={handleManualAdd} 
                disabled={!manualInput.trim()}
                className="bg-gradient-primary hover:opacity-90 shadow-md"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Keywords
              </Button>
            </TabsContent>

            <TabsContent value="file" className="space-y-4 mt-6">
              <div className="space-y-3">
                <Label htmlFor="file-upload" className="text-sm font-medium text-foreground">
                  Upload TXT or CSV file
                </Label>
                <div className="relative">
                  <Input
                    id="file-upload"
                    type="file"
                    accept=".txt,.csv"
                    onChange={handleFileUpload}
                    className="border-border/60 focus:border-primary/60 bg-background/50 backdrop-blur-sm file:bg-gradient-primary file:text-primary-foreground file:border-0 file:rounded-md file:px-3 file:py-1 file:mr-3"
                  />
                </div>
                <p className="text-xs text-muted-foreground">
                  Supported formats: .txt (one keyword per line) or .csv (with headers)
                </p>
              </div>
            </TabsContent>

            <TabsContent value="csv" className="space-y-4 mt-6">
              <div className="space-y-3">
                <Label htmlFor="csv-data" className="text-sm font-medium text-foreground">
                  Paste CSV data
                </Label>
                <div className="relative">
                  <Textarea
                    id="csv-data"
                    placeholder="keyword,kd,volume,intent&#10;best coffee shops,25,1000,local&#10;espresso machine reviews,45,2500,commercial"
                    value={csvInput}
                    onChange={(e) => setCsvInput(e.target.value)}
                    className="min-h-32 resize-none border-border/60 focus:border-primary/60 bg-background/50 backdrop-blur-sm font-mono text-sm"
                  />
                  <div className="absolute top-3 right-3 text-xs text-muted-foreground bg-muted/80 px-2 py-1 rounded">
                    CSV format
                  </div>
                </div>
              </div>
              <Button 
                onClick={() => parseCsvData(csvInput)} 
                disabled={!csvInput.trim()}
                className="bg-gradient-primary hover:opacity-90 shadow-md"
              >
                <Plus className="h-4 w-4 mr-2" />
                Parse CSV Data
              </Button>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {keywords.length > 0 && (
        <Card className="bg-gradient-card border-border shadow-soft">
          <CardHeader>
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  <span>Imported Keywords ({keywords.length})</span>
                </CardTitle>
                <CardDescription>Review and manage your keyword list</CardDescription>
              </div>
              <div className="flex space-x-3">
                <Button variant="outline" size="sm" onClick={clearAll} className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
                <Button
                  variant="default"
                  size="lg"
                  onClick={onAnalyze}
                  disabled={isLoading || keywords.length === 0}
                  className="bg-gradient-primary hover:opacity-90 shadow-md min-w-[200px] relative overflow-hidden"
                >
                  <div className="flex items-center space-x-2">
                    <Sparkles className="h-4 w-4" />
                    <span>{isLoading ? "Analyzing..." : "Analyze & Generate Clusters"}</span>
                  </div>
                  {isLoading && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  )}
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="max-h-96 overflow-y-auto space-y-2 pr-2">
              {keywords.map((keyword, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gradient-to-r from-muted/30 to-muted/10 rounded-xl border border-border/30 hover:border-border/50 transition-all duration-200 animate-slide-in group"
                >
                  <div className="flex-1">
                    <span className="font-medium text-foreground">{keyword.keyword}</span>
                    {(keyword.kd || keyword.volume || keyword.intent) && (
                      <div className="flex space-x-4 text-sm text-muted-foreground mt-2">
                        {keyword.kd && (
                          <span className="bg-orange-500/20 text-orange-300 px-2 py-1 rounded-full text-xs border border-orange-500/30">
                            KD: {keyword.kd}
                          </span>
                        )}
                        {keyword.volume && (
                          <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full text-xs border border-blue-500/30">
                            Volume: {keyword.volume.toLocaleString()}
                          </span>
                        )}
                        {keyword.intent && (
                          <span className="bg-green-500/20 text-green-300 px-2 py-1 rounded-full text-xs border border-green-500/30">
                            Intent: {keyword.intent}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeKeyword(index)}
                    className="text-muted-foreground hover:text-destructive hover:bg-destructive/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
