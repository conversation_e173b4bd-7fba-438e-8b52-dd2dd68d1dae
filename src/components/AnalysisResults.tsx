import { useState } from "react";
import { ChevronDown, ChevronRight, ExternalLink, Link, FileText, Download, Target, Layers, TreePine } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";

export interface SubCluster {
  title: string;
  type: 'long-tail' | 'question-based' | 'local' | 'commercial';
  seoTitle?: string;
  metaDescription?: string;
  urlSlug?: string;
}

export interface Cluster {
  title: string;
  type: 'how-to' | 'question' | 'comparison' | 'list' | 'trending' | 'commercial';
  subClusters: SubCluster[];
  internalLinks: string[];
  externalLinks: string[];
  seoTitle?: string;
  metaDescription?: string;
  urlSlug?: string;
}

export interface Category {
  name: string;
  pillar: string;
  clusters: Cluster[];
  seoTitle?: string;
  metaDescription?: string;
  urlSlug?: string;
}

export interface AnalysisData {
  niche: string;
  categories: Category[];
}

interface AnalysisResultsProps {
  analysisData: AnalysisData;
  onExport: (format: 'csv' | 'json' | 'html') => void;
}

export function AnalysisResults({ analysisData, onExport }: AnalysisResultsProps) {
  const [openCategories, setOpenCategories] = useState<Set<string>>(new Set());
  const [openClusters, setOpenClusters] = useState<Set<string>>(new Set());

  const toggleCategory = (categoryName: string) => {
    const newOpen = new Set(openCategories);
    if (newOpen.has(categoryName)) {
      newOpen.delete(categoryName);
    } else {
      newOpen.add(categoryName);
    }
    setOpenCategories(newOpen);
  };

  const toggleCluster = (clusterTitle: string) => {
    const newOpen = new Set(openClusters);
    if (newOpen.has(clusterTitle)) {
      newOpen.delete(clusterTitle);
    } else {
      newOpen.add(clusterTitle);
    }
    setOpenClusters(newOpen);
  };

  const getTypeColor = (type: string) => {
    const colors = {
      'how-to': 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      'question': 'bg-green-500/20 text-green-300 border-green-500/30',
      'comparison': 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      'list': 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30',
      'trending': 'bg-red-500/20 text-red-300 border-red-500/30',
      'commercial': 'bg-orange-500/20 text-orange-300 border-orange-500/30',
      'long-tail': 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30',
      'question-based': 'bg-emerald-500/20 text-emerald-300 border-emerald-500/30',
      'local': 'bg-indigo-500/20 text-indigo-300 border-indigo-500/30',
    };
    return colors[type as keyof typeof colors] || 'bg-gray-500/20 text-gray-300 border-gray-500/30';
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Niche Header */}
      <Card className="bg-gradient-card border-border shadow-elegant">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-primary rounded-lg">
                <Target className="h-6 w-6 text-primary-foreground" />
              </div>
              <div>
                <CardTitle className="text-2xl">Detected Niche</CardTitle>
                <CardDescription>AI-identified main topic from your keywords</CardDescription>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={() => onExport('csv')}>
                <Download className="h-4 w-4 mr-2" />
                CSV
              </Button>
              <Button variant="outline" size="sm" onClick={() => onExport('json')}>
                <Download className="h-4 w-4 mr-2" />
                JSON
              </Button>
              <Button variant="outline" size="sm" onClick={() => onExport('html')}>
                <Download className="h-4 w-4 mr-2" />
                HTML
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="p-4 bg-primary/10 rounded-lg border border-primary/20">
            <h3 className="text-xl font-bold text-primary mb-2">{analysisData.niche}</h3>
            <p className="text-muted-foreground">
              This niche encompasses {analysisData.categories.length} main categories with{' '}
              {analysisData.categories.reduce((total, cat) => total + cat.clusters.length, 0)} content clusters
              and {analysisData.categories.reduce((total, cat) => total + cat.clusters.reduce((sum, cluster) => sum + cluster.subClusters.length, 0), 0)} sub-topics.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Content Structure */}
      <Card className="bg-gradient-card border-border shadow-soft">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Layers className="h-5 w-5 text-primary" />
            <span>Content Cluster Structure</span>
          </CardTitle>
          <CardDescription>
            Hierarchical content organization with pillar pages, clusters, and sub-clusters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analysisData.categories.map((category, categoryIndex) => (
              <Collapsible
                key={category.name}
                open={openCategories.has(category.name)}
                onOpenChange={() => toggleCategory(category.name)}
              >
                <CollapsibleTrigger asChild>
                  <div className="flex items-center justify-between p-4 bg-muted/30 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors cursor-pointer">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-2">
                        {openCategories.has(category.name) ? (
                          <ChevronDown className="h-5 w-5 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-5 w-5 text-muted-foreground" />
                        )}
                        <TreePine className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground">{category.name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {category.clusters.length} clusters • Pillar: {category.pillar}
                        </p>
                      </div>
                    </div>
                    <Badge variant="secondary" className="bg-primary/20 text-primary border-primary/30">
                      Category
                    </Badge>
                  </div>
                </CollapsibleTrigger>
                
                <CollapsibleContent className="mt-2 ml-8 space-y-3">
                  {/* Pillar Page */}
                  <div className="p-3 bg-primary/5 rounded-lg border border-primary/20">
                    <div className="flex items-center space-x-2 mb-2">
                      <FileText className="h-4 w-4 text-primary" />
                      <span className="font-medium text-primary">Pillar Page</span>
                    </div>
                    <h5 className="text-foreground font-medium">{category.pillar}</h5>
                  </div>

                  {/* SEO Suggestions for Category */}
                  {(category.seoTitle || category.metaDescription || category.urlSlug) && (
                    <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                      <div className="flex items-center space-x-2 mb-3">
                        <Target className="h-4 w-4 text-green-600 dark:text-green-400" />
                        <span className="font-medium text-green-800 dark:text-green-200">SEO Suggestions</span>
                      </div>
                      <div className="space-y-2 text-sm">
                        {category.seoTitle && (
                          <div>
                            <span className="font-medium text-green-700 dark:text-green-300">Title: </span>
                            <span className="text-green-600 dark:text-green-400">{category.seoTitle}</span>
                          </div>
                        )}
                        {category.metaDescription && (
                          <div>
                            <span className="font-medium text-green-700 dark:text-green-300">Description: </span>
                            <span className="text-green-600 dark:text-green-400">{category.metaDescription}</span>
                          </div>
                        )}
                        {category.urlSlug && (
                          <div>
                            <span className="font-medium text-green-700 dark:text-green-300">URL Slug: </span>
                            <span className="text-green-600 dark:text-green-400 font-mono">/{category.urlSlug}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Clusters */}
                  {category.clusters.map((cluster, clusterIndex) => (
                    <Collapsible
                      key={cluster.title}
                      open={openClusters.has(cluster.title)}
                      onOpenChange={() => toggleCluster(cluster.title)}
                    >
                      <CollapsibleTrigger asChild>
                        <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border border-border/30 hover:bg-muted/40 transition-colors cursor-pointer">
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center space-x-2">
                              {openClusters.has(cluster.title) ? (
                                <ChevronDown className="h-4 w-4 text-muted-foreground" />
                              ) : (
                                <ChevronRight className="h-4 w-4 text-muted-foreground" />
                              )}
                              <FileText className="h-4 w-4 text-accent-foreground" />
                            </div>
                            <div>
                              <h6 className="font-medium text-foreground">{cluster.title}</h6>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge className={getTypeColor(cluster.type)}>
                                  {cluster.type}
                                </Badge>
                                <span className="text-xs text-muted-foreground">
                                  {cluster.subClusters.length} sub-clusters
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CollapsibleTrigger>
                      
                      <CollapsibleContent className="mt-2 ml-6 space-y-3">
                        {/* SEO Suggestions for Cluster */}
                        {(cluster.seoTitle || cluster.metaDescription || cluster.urlSlug) && (
                          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div className="flex items-center space-x-2 mb-2">
                              <Target className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                              <span className="font-medium text-blue-800 dark:text-blue-200 text-sm">Cluster SEO</span>
                            </div>
                            <div className="space-y-1 text-xs">
                              {cluster.seoTitle && (
                                <div>
                                  <span className="font-medium text-blue-700 dark:text-blue-300">Title: </span>
                                  <span className="text-blue-600 dark:text-blue-400">{cluster.seoTitle}</span>
                                </div>
                              )}
                              {cluster.metaDescription && (
                                <div>
                                  <span className="font-medium text-blue-700 dark:text-blue-300">Description: </span>
                                  <span className="text-blue-600 dark:text-blue-400">{cluster.metaDescription}</span>
                                </div>
                              )}
                              {cluster.urlSlug && (
                                <div>
                                  <span className="font-medium text-blue-700 dark:text-blue-300">URL: </span>
                                  <span className="text-blue-600 dark:text-blue-400 font-mono">/{cluster.urlSlug}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        {/* Sub-clusters */}
                        {cluster.subClusters.map((subCluster, subIndex) => (
                          <div key={subIndex} className="space-y-2">
                            <div className="flex items-center space-x-2 p-2 bg-muted/10 rounded border border-border/20">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                              <span className="text-sm text-foreground">{subCluster.title}</span>
                              <Badge className={getTypeColor(subCluster.type)} variant="outline">
                                {subCluster.type}
                              </Badge>
                            </div>

                            {/* SEO Suggestions for Sub-cluster */}
                            {(subCluster.seoTitle || subCluster.metaDescription || subCluster.urlSlug) && (
                              <div className="ml-4 p-2 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                                <div className="flex items-center space-x-2 mb-1">
                                  <Target className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                                  <span className="font-medium text-purple-800 dark:text-purple-200 text-xs">Sub-cluster SEO</span>
                                </div>
                                <div className="space-y-1 text-xs">
                                  {subCluster.seoTitle && (
                                    <div>
                                      <span className="font-medium text-purple-700 dark:text-purple-300">Title: </span>
                                      <span className="text-purple-600 dark:text-purple-400">{subCluster.seoTitle}</span>
                                    </div>
                                  )}
                                  {subCluster.metaDescription && (
                                    <div>
                                      <span className="font-medium text-purple-700 dark:text-purple-300">Description: </span>
                                      <span className="text-purple-600 dark:text-purple-400">{subCluster.metaDescription}</span>
                                    </div>
                                  )}
                                  {subCluster.urlSlug && (
                                    <div>
                                      <span className="font-medium text-purple-700 dark:text-purple-300">URL: </span>
                                      <span className="text-purple-600 dark:text-purple-400 font-mono">/{subCluster.urlSlug}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}

                        {/* Linking Information */}
                        <div className="mt-3 p-3 bg-muted/5 rounded border border-border/10">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <h6 className="text-sm font-medium text-foreground mb-2 flex items-center space-x-1">
                                <Link className="h-3 w-3" />
                                <span>Internal Links</span>
                              </h6>
                              <ul className="space-y-1">
                                {cluster.internalLinks.map((link, linkIndex) => (
                                  <li key={linkIndex} className="text-xs text-muted-foreground">
                                    → {link}
                                  </li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <h6 className="text-sm font-medium text-foreground mb-2 flex items-center space-x-1">
                                <ExternalLink className="h-3 w-3" />
                                <span>External Links</span>
                              </h6>
                              <ul className="space-y-1">
                                {cluster.externalLinks.map((link, linkIndex) => (
                                  <li key={linkIndex} className="text-xs text-info">
                                    <a href={link} target="_blank" rel="noopener noreferrer" className="hover:underline">
                                      {new URL(link).hostname}
                                    </a>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}