
import { <PERSON>, Zap, Target, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface SEOToolHeaderProps {
  onApiKeyClick: () => void;
  hasApiKey: boolean;
}

export function SEOToolHeader({ onApiKeyClick, hasApiKey }: SEOToolHeaderProps) {
  return (
    <header className="border-b border-border bg-card/50 backdrop-blur-xl sticky top-0 z-50 shadow-soft">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="relative p-3 bg-gradient-primary rounded-xl shadow-glow">
                <Search className="h-7 w-7 text-primary-foreground" />
                <Sparkles className="absolute -top-1 -right-1 h-4 w-4 text-yellow-400 animate-pulse" />
              </div>
              <div>
                <h1 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-primary via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  SEO Content Strategist
                </h1>
                <p className="text-sm text-muted-foreground font-medium">
                  AI-Powered Content Strategy & Cluster Analysis
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-6 text-sm">
              <div className="flex items-center space-x-2 px-3 py-2 bg-accent/50 rounded-lg">
                <Target className="h-4 w-4 text-primary" />
                <span className="font-medium text-accent-foreground">Smart Niche Detection</span>
              </div>
              <div className="flex items-center space-x-2 px-3 py-2 bg-accent/50 rounded-lg">
                <Zap className="h-4 w-4 text-primary" />
                <span className="font-medium text-accent-foreground">AI Clustering</span>
              </div>
            </div>
            
            <Button
              variant={hasApiKey ? "success" : "warning"}
              size="lg"
              onClick={onApiKeyClick}
              className="animate-fade-in font-semibold shadow-elegant hover:shadow-glow transition-all duration-300"
            >
              {hasApiKey ? "✓ API Connected" : "⚠ Setup API Key"}
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
