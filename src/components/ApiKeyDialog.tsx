import { useState, useEffect } from "react";
import { <PERSON>, Eye, EyeOff, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ApiKeyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApiKeySaved: (apiKey: string) => void;
  currentApiKey?: string;
}

export function ApiKeyDialog({ open, onOpenChange, onApiKeySaved, currentApiKey }: ApiKeyDialogProps) {
  const [apiKey, setApiKey] = useState(currentApiKey || "");
  const [showKey, setShowKey] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  useEffect(() => {
    if (currentApiKey) {
      setApiKey(currentApiKey);
    }
  }, [currentApiKey]);

  const handleSave = async () => {
    if (!apiKey.trim()) return;
    
    setIsValidating(true);
    
    // Simple validation - just check if it starts with expected prefix
    const isValid = apiKey.startsWith('AIzaSy') || apiKey.length > 30;
    
    setTimeout(() => {
      setIsValidating(false);
      if (isValid) {
        onApiKeySaved(apiKey);
        onOpenChange(false);
      }
    }, 1000);
  };

  const handleClear = () => {
    setApiKey("");
    localStorage.removeItem('gemini_api_key');
    onApiKeySaved("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-primary" />
            <span>Gemini API Configuration</span>
          </DialogTitle>
          <DialogDescription>
            Connect your Gemini API key to enable AI-powered content clustering and niche detection.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Alert>
            <AlertDescription className="text-sm">
              Your API key is stored locally in your browser and never sent to our servers. 
              It's only used for direct communication with Google's Gemini API.
            </AlertDescription>
          </Alert>

          <div className="space-y-2">
            <Label htmlFor="api-key">Gemini API Key</Label>
            <div className="relative">
              <Input
                id="api-key"
                type={showKey ? "text" : "password"}
                placeholder="Enter your Gemini API key..."
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowKey(!showKey)}
              >
                {showKey ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </div>
          </div>

          <Card className="bg-muted/30">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">How to get your API key:</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <ol className="text-sm text-muted-foreground space-y-1">
                <li>1. Visit Google AI Studio</li>
                <li>2. Sign in with your Google account</li>
                <li>3. Click "Get API key" in the top navigation</li>
                <li>4. Create a new API key for your project</li>
                <li>5. Copy and paste it here</li>
              </ol>
              <Button
                variant="link"
                size="sm"
                className="mt-2 p-0 h-auto text-primary"
                onClick={() => window.open('https://aistudio.google.com/app/apikey', '_blank')}
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Open Google AI Studio
              </Button>
            </CardContent>
          </Card>

          <div className="flex justify-between space-x-2">
            {currentApiKey && (
              <Button variant="outline" onClick={handleClear}>
                Clear Key
              </Button>
            )}
            <div className="flex space-x-2 ml-auto">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleSave} 
                disabled={!apiKey.trim() || isValidating}
                variant="default"
              >
                {isValidating ? "Validating..." : "Save Key"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}