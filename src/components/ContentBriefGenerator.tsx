import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Copy, FileText, Users, Target, TrendingUp, Clock, Eye, Settings, Lightbulb } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { ApiConfig } from "./ApiConfigDialog";
import { createAIService } from "@/services/aiService";

interface BriefData {
  keyword: string;
  contentType: string;
  targetAudience: string;
  contentGoal: string;
}

interface ContentBrief {
  title: string;
  metaDescription: string;
  targetAudience: {
    primary: string;
    demographics: string;
    painPoints: string[];
    goals: string[];
  };
  contentOutline: {
    h1: string;
    sections: {
      h2: string;
      h3s: string[];
      keyPoints: string[];
    }[];
  };
  wordCount: {
    recommended: number;
    range: string;
    reasoning: string;
  };
  competitorAnalysis: {
    topCompetitors: string[];
    contentGaps: string[];
    opportunities: string[];
  };
  seoRecommendations: {
    primaryKeyword: string;
    secondaryKeywords: string[];
    lsiKeywords: string[];
    internalLinks: string[];
  };
  contentStrategy: {
    angle: string;
    tone: string;
    cta: string;
    contentType: string;
  };
}

interface ContentBriefGeneratorProps {
  apiConfig?: ApiConfig | null;
  onApiKeyRequest?: () => void;
}

export function ContentBriefGenerator({ apiConfig, onApiKeyRequest }: ContentBriefGeneratorProps) {
  const [briefData, setBriefData] = useState<BriefData>({
    keyword: "",
    contentType: "",
    targetAudience: "",
    contentGoal: ""
  });

  const [generatedBrief, setGeneratedBrief] = useState<ContentBrief | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: keyof BriefData, value: string) => {
    setBriefData(prev => ({ ...prev, [field]: value }));
  };

  const generateWithAI = async (prompt: string): Promise<string> => {
    if (!apiConfig) {
      throw new Error('No API configuration available');
    }

    const aiService = createAIService(apiConfig);
    return await aiService.generateContent(prompt);
  };

  const generateBrief = async () => {
    if (!apiConfig) {
      if (onApiKeyRequest) {
        onApiKeyRequest();
      }
      return;
    }

    if (!briefData.keyword) {
      toast({
        title: "Missing Information",
        description: "Please provide a target keyword.",
        variant: "destructive"
      });
      return;
    }

    setIsGenerating(true);

    try {
      const prompt = `Create a comprehensive content brief for SEO content creation with the following requirements:

Target Keyword: ${briefData.keyword}
Content Type: ${briefData.contentType || 'Blog Post'}
Target Audience: ${briefData.targetAudience || 'General audience interested in the topic'}
Content Goal: ${briefData.contentGoal || 'Educate and inform'}

Please generate a detailed content brief including:

1. SEO-optimized title and meta description
2. Detailed target audience analysis (demographics, pain points, goals)
3. Comprehensive content outline with H1, H2s, H3s, and key points for each section
4. Recommended word count with reasoning
5. Competitor analysis with content gaps and opportunities
6. SEO recommendations (primary/secondary keywords, LSI keywords, internal linking)
7. Content strategy (angle, tone, CTA, content type)

Make everything specific, actionable, and tailored to the keyword "${briefData.keyword}".

Format the response as JSON with this exact structure:
{
  "title": "SEO-optimized title",
  "metaDescription": "150-160 character meta description",
  "targetAudience": {
    "primary": "Primary audience description",
    "demographics": "Age, location, profession, interests",
    "painPoints": ["Pain point 1", "Pain point 2", "Pain point 3"],
    "goals": ["Goal 1", "Goal 2", "Goal 3"]
  },
  "contentOutline": {
    "h1": "Main title/H1",
    "sections": [
      {
        "h2": "Section title",
        "h3s": ["Subsection 1", "Subsection 2"],
        "keyPoints": ["Key point 1", "Key point 2", "Key point 3"]
      }
    ]
  },
  "wordCount": {
    "recommended": 2500,
    "range": "2000-3000 words",
    "reasoning": "Explanation for word count recommendation"
  },
  "competitorAnalysis": {
    "topCompetitors": ["Competitor 1", "Competitor 2", "Competitor 3"],
    "contentGaps": ["Gap 1", "Gap 2", "Gap 3"],
    "opportunities": ["Opportunity 1", "Opportunity 2", "Opportunity 3"]
  },
  "seoRecommendations": {
    "primaryKeyword": "Main keyword",
    "secondaryKeywords": ["Secondary 1", "Secondary 2", "Secondary 3"],
    "lsiKeywords": ["LSI 1", "LSI 2", "LSI 3", "LSI 4"],
    "internalLinks": ["Link suggestion 1", "Link suggestion 2", "Link suggestion 3"]
  },
  "contentStrategy": {
    "angle": "Content angle/approach",
    "tone": "Writing tone",
    "cta": "Call-to-action suggestion",
    "contentType": "Content format"
  }
}

Ensure all recommendations are specific, actionable, and optimized for search engines and user engagement.`;

      const response = await generateWithAI(prompt);
      
      // Clean up the response to extract JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const briefResult = JSON.parse(jsonMatch[0]);
        setGeneratedBrief(briefResult);
        
        toast({
          title: "Content Brief Generated!",
          description: "Your comprehensive content brief is ready.",
        });
      } else {
        throw new Error("Invalid response format");
      }
    } catch (error) {
      console.error('Error generating brief:', error);
      toast({
        title: "Generation Failed",
        description: error instanceof Error ? error.message : "Failed to generate brief. Please check your API key and try again.",
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied!",
      description: `${type} copied to clipboard.`,
    });
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="p-3 bg-gradient-to-r from-green-600 to-blue-600 rounded-xl shadow-lg">
            <FileText className="h-8 w-8 text-white" />
          </div>
          <div>
            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-green-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
              Content Brief Generator
            </h1>
            <p className="text-xl text-muted-foreground">Generate comprehensive content briefs in seconds</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Generator Controls */}
        <Card className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border-border/50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-primary" />
              <span>Brief Configuration</span>
            </CardTitle>
            <CardDescription>
              Configure your content brief parameters
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">


            {/* Input Fields */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="keyword">Target Keyword *</Label>
                <Input
                  id="keyword"
                  placeholder="e.g., best project management software"
                  value={briefData.keyword}
                  onChange={(e) => handleInputChange('keyword', e.target.value)}
                  className="text-base"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contentType">Content Type</Label>
                <Select value={briefData.contentType} onValueChange={(value) => handleInputChange('contentType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select content type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="blog-post">Blog Post</SelectItem>
                    <SelectItem value="guide">Complete Guide</SelectItem>
                    <SelectItem value="tutorial">Tutorial</SelectItem>
                    <SelectItem value="comparison">Comparison Article</SelectItem>
                    <SelectItem value="listicle">Listicle</SelectItem>
                    <SelectItem value="case-study">Case Study</SelectItem>
                    <SelectItem value="review">Product Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="audience">Target Audience</Label>
                <Input
                  id="audience"
                  placeholder="e.g., Small business owners, Marketing managers"
                  value={briefData.targetAudience}
                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="goal">Content Goal</Label>
                <Select value={briefData.contentGoal} onValueChange={(value) => handleInputChange('contentGoal', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select content goal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="educate">Educate & Inform</SelectItem>
                    <SelectItem value="convert">Drive Conversions</SelectItem>
                    <SelectItem value="engage">Increase Engagement</SelectItem>
                    <SelectItem value="brand-awareness">Build Brand Awareness</SelectItem>
                    <SelectItem value="lead-generation">Generate Leads</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              onClick={generateBrief}
              disabled={isGenerating || !briefData.keyword || !apiConfig}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
              size="lg"
            >
              <Lightbulb className="h-5 w-5 mr-2" />
              {isGenerating ? "Generating Brief..." : "Generate Content Brief"}
            </Button>
          </CardContent>
        </Card>

        {/* Generated Brief Preview */}
        {generatedBrief && (
          <Card className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm border-border/50">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5 text-primary" />
                <span>Content Brief</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 max-h-[600px] overflow-y-auto">
              {/* Title & Meta */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="font-medium">SEO Title</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBrief.title, "Title")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
                  <p className="font-medium text-primary">{generatedBrief.title}</p>
                </div>

                <div className="flex items-center justify-between">
                  <Label className="font-medium">Meta Description</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(generatedBrief.metaDescription, "Meta Description")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="p-3 bg-muted/30 rounded-lg text-sm">
                  {generatedBrief.metaDescription}
                </div>
              </div>

              <Separator />

              {/* Target Audience */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-blue-600" />
                  <Label className="font-medium">Target Audience</Label>
                </div>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Primary: </span>
                    <span>{generatedBrief.targetAudience.primary}</span>
                  </div>
                  <div>
                    <span className="font-medium">Demographics: </span>
                    <span>{generatedBrief.targetAudience.demographics}</span>
                  </div>
                  <div>
                    <span className="font-medium">Pain Points: </span>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      {generatedBrief.targetAudience.painPoints.map((point, index) => (
                        <li key={index}>{point}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Word Count */}
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-green-600" />
                  <Label className="font-medium">Word Count Recommendation</Label>
                </div>
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="text-sm space-y-1">
                    <div><span className="font-medium">Recommended: </span>{generatedBrief.wordCount.recommended} words</div>
                    <div><span className="font-medium">Range: </span>{generatedBrief.wordCount.range}</div>
                    <div><span className="font-medium">Reasoning: </span>{generatedBrief.wordCount.reasoning}</div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Content Outline */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4 text-orange-600" />
                  <Label className="font-medium">Content Outline</Label>
                </div>
                <div className="space-y-3">
                  <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                    <div className="font-medium text-orange-800 dark:text-orange-200 mb-2">H1: {generatedBrief.contentOutline.h1}</div>
                    <div className="space-y-2">
                      {generatedBrief.contentOutline.sections.map((section, index) => (
                        <div key={index} className="ml-2">
                          <div className="font-medium text-sm text-orange-700 dark:text-orange-300">H2: {section.h2}</div>
                          {section.h3s.length > 0 && (
                            <div className="ml-4 mt-1 space-y-1">
                              {section.h3s.map((h3, h3Index) => (
                                <div key={h3Index} className="text-xs text-orange-600 dark:text-orange-400">H3: {h3}</div>
                              ))}
                            </div>
                          )}
                          {section.keyPoints.length > 0 && (
                            <div className="ml-4 mt-1">
                              <div className="text-xs text-orange-600 dark:text-orange-400">Key Points:</div>
                              <ul className="list-disc list-inside ml-2 text-xs text-orange-500 dark:text-orange-500">
                                {section.keyPoints.map((point, pointIndex) => (
                                  <li key={pointIndex}>{point}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* SEO Keywords */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Target className="h-4 w-4 text-purple-600" />
                  <Label className="font-medium">SEO Keywords</Label>
                </div>
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium">Primary: </span>
                    <Badge variant="default">{generatedBrief.seoRecommendations.primaryKeyword}</Badge>
                  </div>
                  <div>
                    <span className="text-sm font-medium">Secondary: </span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {generatedBrief.seoRecommendations.secondaryKeywords.map((keyword, index) => (
                        <Badge key={index} variant="secondary">{keyword}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium">LSI Keywords: </span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {generatedBrief.seoRecommendations.lsiKeywords.map((keyword, index) => (
                        <Badge key={index} variant="outline">{keyword}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Competitor Analysis */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-red-600" />
                  <Label className="font-medium">Competitor Analysis</Label>
                </div>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="font-medium">Top Competitors: </span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {generatedBrief.competitorAnalysis.topCompetitors.map((competitor, index) => (
                        <Badge key={index} variant="destructive">{competitor}</Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <span className="font-medium">Content Gaps: </span>
                    <ul className="list-disc list-inside mt-1 space-y-1 text-red-600 dark:text-red-400">
                      {generatedBrief.competitorAnalysis.contentGaps.map((gap, index) => (
                        <li key={index}>{gap}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <span className="font-medium">Opportunities: </span>
                    <ul className="list-disc list-inside mt-1 space-y-1 text-green-600 dark:text-green-400">
                      {generatedBrief.competitorAnalysis.opportunities.map((opportunity, index) => (
                        <li key={index}>{opportunity}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Content Strategy */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Lightbulb className="h-4 w-4 text-yellow-600" />
                  <Label className="font-medium">Content Strategy</Label>
                </div>
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <div className="space-y-2 text-sm">
                    <div><span className="font-medium">Angle: </span>{generatedBrief.contentStrategy.angle}</div>
                    <div><span className="font-medium">Tone: </span>{generatedBrief.contentStrategy.tone}</div>
                    <div><span className="font-medium">CTA: </span>{generatedBrief.contentStrategy.cta}</div>
                    <div><span className="font-medium">Format: </span>{generatedBrief.contentStrategy.contentType}</div>
                  </div>
                </div>
              </div>

              {/* Copy Full Brief Button */}
              <Button
                onClick={() => {
                  const fullBrief = `CONTENT BRIEF: ${generatedBrief.title}\n\nMeta Description: ${generatedBrief.metaDescription}\n\nTarget Audience: ${generatedBrief.targetAudience.primary}\n\nWord Count: ${generatedBrief.wordCount.recommended} words (${generatedBrief.wordCount.range})\n\nPrimary Keyword: ${generatedBrief.seoRecommendations.primaryKeyword}\n\nContent Outline:\n${generatedBrief.contentOutline.sections.map(section => `\n${section.h2}\n${section.h3s.map(h3 => `  - ${h3}`).join('\n')}`).join('\n')}`;
                  copyToClipboard(fullBrief, "Full Content Brief");
                }}
                className="w-full"
                variant="outline"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Full Brief
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
