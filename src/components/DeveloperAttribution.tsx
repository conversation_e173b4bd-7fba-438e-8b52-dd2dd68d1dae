
import { Github, ExternalLink } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

export function DeveloperAttribution() {
  return (
    <Card className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-800 dark:via-blue-900 dark:to-indigo-900 border-blue-200 dark:border-blue-800 shadow-lg">
      <CardContent className="p-6">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <Github className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm text-muted-foreground">Developed by</span>
          </div>
          <a 
            href="https://github.com/alindevx00x/"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200 font-medium"
          >
            <span>alindevx00x</span>
            <ExternalLink className="h-4 w-4" />
          </a>
        </div>
      </CardContent>
    </Card>
  );
}
